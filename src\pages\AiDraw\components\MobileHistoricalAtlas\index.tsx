import React, { useEffect, useState } from 'react';
import { observer } from "mobx-react-lite";
import { useTranslation } from 'react-i18next';
import { useStore } from '@/models';
import { LayoutAI_App } from '@/Apps/LayoutAI_App';
import { AI2DesignManager } from '@/Apps/AI2Design/AI2DesignManager';
import useStyles from './style';
import { Image, PaginationProps, Tooltip } from '@svg/antd';
import { AIGCService } from '@/Apps/LayoutAI/Services/AIGC/AIGCService';
import IconFont from '@/components/IconFont/iconFont';
import { RenderReqOffline } from '@/Apps/LayoutAI/Scene3D/light/req/RenderReqOffline';
import { getImgDomain } from '@svg/request';
import { Segmented } from '@svg/antd'
import { Pagination } from '@svg/antd'; // 引入分页组件

interface MobileHistoricalAtlasProps {
    setZIndexOfMobileAtlas: (zIndex: number) => void; // 定义 props 类型
}

export enum atlasMode {
    aidraw = "aidraw",
    render = "render", // 标准渲染
    panoRender = "panoRender" // 全景渲染
}

const App: React.FC<MobileHistoricalAtlasProps> = ({ setZIndexOfMobileAtlas }) => {
    const { t } = useTranslation()
    let store = useStore();
    const { styles } = useStyles();
    const [picList, setPicList] = useState([]);
    const [pageIndexAidraw, setPageIndexAidraw] = useState(1);
    const [pageIndexRender, setPageIndexRender] = useState(1);
    const [pageIndexPanoRender, setPageIndexPanoRender] = useState(1);
    const [pageSize, setPageSize] = useState(15);
    const [pageNum, setPageNum] = useState(1);
    const [total, setTotal] = useState(0);
    const [failedImages, setFailedImages] = useState<Set<string>>(new Set());
    const [previewVisible, setPreviewVisible] = useState<{[key: string]: boolean}>({});
    const [iframeKey, setIframeKey] = useState<{[key: string]: number}>({});

    const topTabItems: {[key:string]: atlasMode} = {
        "标准渲染": atlasMode.render,
        "全景渲染": atlasMode.panoRender,
        "AI绘图": atlasMode.aidraw
    };

    LayoutAI_App.UseApp(AI2DesignManager.AppName); // 确保当前的app_id
    if (LayoutAI_App.instance) {
        LayoutAI_App.t = t;
    }

    const getScheme = async () => {
        let res = await AIGCService.instance.queryImageList(
            '',
            pageIndexAidraw,
            pageSize
        );
        setPicList(res.result);
        setTotal(res.recordCount);
        setPageNum(pageIndexAidraw);
    }

    const getRenderScheme = async () => {
        let res = await RenderReqOffline.instance.requestAtlas(
            {
                userId: '', // 用户ID
                flag: 'normal_all', // 任务类型
                schemeId: '', // 方案ID
                pageIndex: pageIndexRender,
                pageSize: pageSize,
                all: '', // 标志
                isRtx: 0, // RTX 标识
                resolutionTag: '', // 分辨率标签
                type: 0, // 标识类型
                exclusiveRealAdjustLight: false, // 排除实时调光数据
                authCode: ''
            }
        );
        if(res.success) {
            let data = res?.res?.data
            let dataList = res?.res?.data?.ReturnList;
            if(dataList && dataList?.length > 0) {
                dataList.forEach((item: any) => {
                    item.imageResult = item.Status === 3 ? `${getImgDomain()}/${item.FileIdOutPut2}` : null;
                    item.layoutName = item.Remark
                    item.createDate = item.CreateDate
                    item.imageResultList = [''];
                });
                setPicList(dataList);
                setTotal(data.TotalResults);
                setPageNum(pageIndexRender);
            }
        }
    }

    const getPanoRenderScheme = async () => {
        let res = await RenderReqOffline.instance.requestAtlas(
            {
                userId: '', // 用户ID
                flag: 'panorama_all', // 任务类型
                schemeId: '', // 方案ID
                pageIndex: pageIndexPanoRender,
                pageSize: pageSize,
                all: '', // 标志
                isRtx: 0, // RTX 标识
                resolutionTag: '', // 分辨率标签
                type: 1, // 标识类型
                exclusiveRealAdjustLight: false, // 排除实时调光数据
                authCode: ''
            }
        );
        if(res.success) {
            let data = res?.res?.data
            let dataList = res?.res?.data?.ReturnList;
            if(dataList && dataList?.length > 0) {
                dataList.forEach((item: any) => {
                    item.imageResult = item.Status === 3 ? `${getImgDomain()}/${item.FileIdOutPut2}` : null;
                    item.layoutName = item.Remark
                    item.createDate = item.CreateDate
                    item.imageResultList = [''];
                });
                setPicList(dataList);
                setTotal(data.TotalResults);
                setPageNum(pageIndexPanoRender);
            }
        }
    }

    useEffect(() => {
        if(store.homeStore.atlasMode === atlasMode.aidraw) {
            getScheme();
        } else if(store.homeStore.atlasMode === atlasMode.render) {
            getRenderScheme();
        } else if(store.homeStore.atlasMode === atlasMode.panoRender) {
            getPanoRenderScheme();
        }
        setFailedImages(new Set());
    }, [pageIndexAidraw, pageIndexRender, pageIndexPanoRender, store.homeStore.atlasMode]);

    useEffect(() => {
        if (store.homeStore.refreshAtlas) {
            setPageIndexAidraw(1);
            getScheme();
            store.homeStore.setRefreshAtlas(false);
        }
    }, [store.homeStore.refreshAtlas]);

    const scrollGet = (e: React.UIEvent<HTMLDivElement>) => {
        // const target = e.target as HTMLDivElement;
        // if (target.scrollTop + target.offsetHeight >= target.scrollHeight) {
        //     if (picList.length < total) {
        //         setPageIndex(pageIndex + 1);
        //     }
        // }
    }

    const handleImageError = (imageUrl: string) => {
        setFailedImages(prev => new Set(prev).add(imageUrl));
    };

    const CardPagination: React.FC = () => {
        const onChange: PaginationProps['onChange'] = (page) => {
            if(store.homeStore.atlasMode === atlasMode.aidraw){
                setPageIndexAidraw(page);
            } else if (store.homeStore.atlasMode === atlasMode.render) {
                setPageIndexRender(page);
            } else if (store.homeStore.atlasMode === atlasMode.panoRender) {
                setPageIndexPanoRender(page);
            }
        };

        return (
            <Pagination
                simple={true}
                defaultCurrent={1}
                current={
                store.homeStore.atlasMode === atlasMode.aidraw
                    ? pageIndexAidraw
                    : store.homeStore.atlasMode === atlasMode.render
                    ? pageIndexRender
                    : pageIndexPanoRender
                }
                onChange={onChange}
                total={total}
                pageSize={15}
                showSizeChanger={false}
            />
        );
    };

    return (
        <div className={styles.root}>
            
            <div className='atlas_header'>
                <Segmented options={Object.keys(topTabItems)} 
                    onChange={(value => {
                        store.homeStore.setAtlasMode(topTabItems[value]);
                    })}
                    className='segmented'/>
                {
                    store.homeStore.showAtlas === true
                    ? <div className='back_button' onClick={() => store.homeStore.setShowAtlas(false)}>
                        <IconFont style={{ margin: '7px 2px 7px 12px' }} type="icon-line_left" />
                        <span style={{ height: 22, width: 28 }}>{t('返回')}</span>
                    </div>
                    : null
                }
            </div>
            
            {picList.length <= 0 && (
                <div className={styles.noData}>{t('-暂无数据-')}</div>
            )}
            <div className={styles.content} onScroll={scrollGet}>
                {picList.map((item, index) => (
                    <div className={styles.item} key={"history_" + index} onClick={() => null}>
                        {item.imageResult && !failedImages.has(item.imageResult) ? (
                            <div className='main_img_container'>
                                {/* <Image onError={() => handleImageError(item.imageResult)} src={item.imageResult}></Image>
                                <div className='number_tag'>{item.imageResultList.length}</div> */}
                                <Image
                                    preview={store.homeStore.atlasMode == atlasMode.panoRender ? {
                                        imageRender: () => {
                                            const { SchemeId, QueueId } = item;
                                            const itemKey = `${SchemeId}_${QueueId}`;
                                            const currentKey = iframeKey[itemKey] || 0;
                                            return (
                                                <iframe
                                                    key={`iframe_${itemKey}_${currentKey}`}
                                                    width="100%"
                                                    height="100%"
                                                    style={{ border: 'none' }}
                                                    src={`https://dev-pre-pano.3vjia.com:8086/?schemeId=${SchemeId}&queueId=${QueueId}&t=${Date.now()}`}
                                                />
                                            );
                                        },
                                        toolbarRender: () => null,
                                        onVisibleChange: (visible) => {
                                            const { SchemeId, QueueId } = item;
                                            const itemKey = `${SchemeId}_${QueueId}`;
                                            setPreviewVisible(prev => ({
                                                ...prev,
                                                [itemKey]: visible
                                            }));

                                            // 当预览关闭时，增加key值来强制重新创建iframe
                                            if (!visible) {
                                                setIframeKey(prev => ({
                                                    ...prev,
                                                    [itemKey]: (prev[itemKey] || 0) + 1
                                                }));
                                            }
                                        },
                                    } : true}
                                    onError={() => handleImageError(item.imageResult)}
                                    src={item.imageResult}
                                ></Image>
                                <div className='number_tag'>{index + 1 + (pageNum - 1) * 15}</div>
                            </div>
                        ) : (
                            <div className='main_loading_container'>
                                <img src={'https://3vj-fe.3vjia.com/layoutai/image/aidraw_logo.png'} alt="" style={{ height: '40px' }} />
                                <span>{t('生成中...')}</span>
                            </div>
                        )}
                        <div className='info_content'>
                            <Tooltip title={t(item.layoutName)}>
                                <span className='name'>{t(item.layoutName)}</span>
                            </Tooltip>
                            <Tooltip title={t(item.createDate)}>
                                <span className='time'>{t(item.createDate.split(' ')[0])}</span>
                            </Tooltip>
                        </div>
                    </div>
                ))}
            </div>
            { (
                <div key="tablePagination" className={styles.PageContainer}>
                    <CardPagination />
                </div>
            )}
        </div>
    );
};

export default observer(App);
