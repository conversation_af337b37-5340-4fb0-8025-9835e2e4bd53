import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {

    leftPanelRoot: css`
      position:fixed;
      background : #fff;
      z-index: 10;
      .closeBtn {
        display:none;
        position:absolute;
         right : 6px;
         top : 6px;
         font-size:20px;
         width:60px;
         height : 24px;
         text-align:right;
      }
      &.panel_hide {
        box-shadow: 0px 0px 0px 0px #00000000;
      }
      @media screen and (orientation: landscape) {
        position:fixed;
        left: 12px !important;
        top: 52px !important;
        bottom: 12px !important;
        right: auto !important;
        height: auto;
        padding-left: 0 !important;
        max-height: calc(var(--vh, 1vh) * 100);
        max-width:224px;
        width: 224px;
        border-radius: 8px;
        box-shadow:  0px 0px 16px 10px #0000000A;
        &.panel_hide {
          display: none;
        }
      }
      @media screen and (orientation: portrait) {
        position:fixed;
        left:0;
        bottom:0px;
        right:0;
        width : auto;
        height:340px;
        max-width : auto;
        max-height:340px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 8px 8px 0px 0px;
        box-shadow:  0px 0px 16px 10px #0000000A;
        @media screen and (-webkit-min-device-pixel-ratio:2) and (max-height:700px) {
          transform : scale(0.7);
          transform-origin : bottom;
          left : -15%;
          right : -15%;
        }
        &.panel_hide {
          max-width : 0px;
        }
        .closeBtn {
          display : block;
        }
      }


      .fade-enter {
        opacity: 0;
      }

      .fade-enter-active {
        opacity: 1;
        transition: opacity 300ms ease-in-out;
      }

      .fade-exit {
        opacity: 1;
      }

      .fade-exit-active {
        opacity: 0;
        transition: opacity 300ms ease-in-out;
      }
    `,
    materialReplace: css`
      background: rgba(0, 0, 0, 0.40) !important;
      backdrop-filter: blur(50px) !important;
    `,
    tabBar: css`
      .tabItem {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 12px;
        flex: 1 0 0;
        padding: 16px 12px 0 12px;
        .item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          gap: 2px;
          flex: 1 0 0;
          .label {
            align-self: stretch;
            color: #959598;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 150%; /* 24px */
          }
          .line {
            width: 20px;
            height: 3px;
            border-radius: 3px;
            background: #ffffff;
          }
          &.active {
            .line {
              width: 20px;
              height: 3px;
              border-radius: 3px;
              background: #282828;
            }
            .label {
              color: #282828;
              font-weight: 600;
              line-height: 150%; /* 24px */
            }
          }
        }
      }
    `,
    leftTabBar: css`
      position: absolute;
      left: 5px;
      width: 34px;
      top: 50px;
      height:210px;
      display: flex;
      flex-direction: column;
      font-size: 17px;
      align-items: center;
      flex-wrap: nowrap;
      justify-content: center;
      border-radius:8px;
      color : #aaa;
      text-align:center;
      background:#eee;

      @media screen and (orientation: landscape) {
          display:none;
      }
      @media screen and (orientation: portrait) {
        display: flex;
        
      }
      .vTab {
        width: 30px;
        padding-top: 8px;
        padding-bottom: 8px;
        padding-left:5px;
        padding-right:5px;

        &.checked {
          background:#fff;
          color : #2b2b2b;
          border-radius:8px;

        }

      }
    `,
    collapseBtn: css`
      display:none;
      width: 20px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      background-color: #fff;
      border-radius: 0px 6px 6px 0px;
      box-shadow: 0px -16px 16px 0px #00000005;
      cursor:pointer;

      @media screen and (orientation: landscape) {
        display:block;
        position: fixed;
        left: 235px;
        top: calc(50% - 48px);
        z-index: 9;
      }
      @media screen and (orientation: portrait) {
        position: fixed;
        bottom: 120px;
        left: 0px;
        z-index: 999;

      }
      &.panel_hide {
        left:0px;
        display:block;
      }
    `,
    tab_root: css`
      color : #959598;
      .ant-tabs-tab {
        color :#959598;
      }
    `,
    topTitle: css`
      display: flex;
      height: 40px;
      padding: 0 24px;
      align-items: center;
      font-size: 20px;
      color: #282828;
      font-weight: 600;
      margin-top: 16px;
      justify-content: space-between;
      @media screen and (max-width: 450px) { // 手机宽度
        height: 15px;
        font-size: 16px;
      }
      @media screen and (orientation: landscape) {
        height: 40px;
        font-size: 14px;
      }

    `,
    popupContainer: css`
      /* position:absolute;
      left:45px;
      right:5px;
      top: 0px;
      bottom:10px;
      overflow:hidden;
      @media screen and (orientation: landscape) {
        left:5px;
        top: 0px;

      } */
    `,
    listContainer: css`
      height:100%;
      width:100%;
      .dropdown {
        width: calc(100% - 24px);
        height: 26px;
        box-sizing: border-box;
        padding: 0 12px;
        margin: 16px 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.15);
        background: #FFFFFF;
      }
    `,
    open: css`
      position: absolute; 
      right: 10px;
      top: 10px;
      z-index: 9;
    `,
    navigation: css`
  `,
  backBtn: css`
    position: fixed;
    top: 12px;
    left: 12px;
    height: 32px;
    width: 68px;
    line-height:28px;
    font-size: 14px;
    background:#fff;
    border-radius:6px;
    text-align:center;
    display: flex;
    align-items: center;
    justify-content: center;
    .iconleft {
      text-align:left;
    }
    @media screen and (max-width: 450px) { // 手机宽度
      font-size: 12px;
      width:50px;

    }
    z-index: 9;
  `,
  header: css`
    position: fixed;
    top: 0;
    right: 0;
    display: flex;
    width: 100%;
    height: 52px;
    padding: 6px 20px;
    box-sizing: border-box;
    align-items: center;
    gap: 16px;
    align-self: stretch;
    z-index: 9;
    .icon {
      width: 20px;
      height: 20px;
      cursor: pointer;
    }
    .center {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
      .segmented {
        display: flex;
        padding: 2px;
        align-items: center;
        border-radius: 100px;
        background: #E9EBEB;
        border: none;
        height: 38px;
        
        .ant-segmented-item {
          display: flex;
          padding: 6px 16px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 100px;
          border: none;
          height: 34px;
          &-input {
            display: none;
          }

          .ant-segmented-item-label {
            color: #5B5E60;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
          }
          
          &.ant-segmented-item-selected {
            background: #FFF;
            
            .ant-segmented-item-label {
              color: #282828;
              font-weight: 600;
              padding: 0;
            }
          }
        }
      }
    }
  `,
  blackColor: css`
    background: rgba(0, 0, 0, 0.40);
    backdrop-filter: blur(50px);
    color: #fff;
  `,
    forwardBtn: css`
    position: fixed;
    top: 12px;
    right: 12px;
    height : 28x;
    width:60px;
    line-height:28px;
    color: #fff;
    font-size: 13px;
    border-radius:6px;
    text-align:center;
    background: linear-gradient(90deg, #BA63F0 0%, #5C42FB 100%);
    z-index:9;
    @media screen and (max-width: 450px) { // 手机宽度
      font-size: 12px;
    }
  `,
    closeBtn: css`
      position: fixed;
      top: 6px;
      right: 12px;
      width:40px;
      height : 40px;
      line-height:40px;
      border-radius:6px;
      text-align:center;
      color :#282828;
      background:rgba(255,255,255,0.2);
      z-index:9;
      font-size: 16px;
      @media screen and (max-width: 450px) { // 手机宽度
        font-size: 14px;
      }

   `,
    shareBarContainer: css`
    top: 50px;
    right: 12px;
    position: fixed;
    z-index:999;
    background:#fff;
    padding:10px;
    border-radius:6px;
  `,
    topTabs: css`
      position:fixed;
      width: 200px;
      left: calc(50% - 100px);
      top: 0px;
      @media screen and (max-width: 450px) { // 手机宽度
        width:120px;
        left: calc(50% - 60px);
      }
      @media screen and (max-width: 350px) { // 手机宽度
        top: 60px;
        left :12px;
      }
      z-index: 9;
    `,
    rightSceneModeTabs: css`
        position:fixed;
        width: 200px;
        right: 12px;
        top: 5px;
        z-index: 9;
    `,
    bottomButtons: css`
      position:fixed;
      bottom: 20px;
      z-index: 9;
      display:flex;
      justify-content:center;
      align-items:center;
      gap: 16px;
      left: 50%;
      transform: translate(-50%, 0);
      transition: all 0.5s ease;
      .btn {
        display: flex;
        /* width: 102px; */
        padding: 8px 40px;
        height: 37px;
        box-sizing: border-box;
        justify-content: center;
        align-items: center;
        flex: 1 0 0;
        align-self: stretch;
        border-radius: 50px;
        border-top: 1px solid rgba(0, 0, 0, 0.06);
        background: #FFF;
        box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.08);

        color: #282828;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        text-align: center;
      }
      .blackColor {
        background: rgba(0, 0, 0, 0.40) !important;
        backdrop-filter: blur(50px) !important;
        color: #fff !important;
      }
      @media screen and (orientation: landscape){
        display:flex;
        justify-content:center;
        align-items:center;
        left: 50%;
        transform: translate(-50%, 0);
      }
      &.showLeftPanel {
        @media screen and (orientation: portrait) {
          position: fixed;
          bottom: 280px;
          max-width : 48px;
          top : auto;;
          left: 44%;
          transform: translateX(-50%);
          right : 24px;
          height: 120px;
          display: block;

          @media screen and (max-width: 450px) { // 手机宽度
            right : 12px;
          }
          @media screen and (max-height: 700px) { 
            right : auto;
            left : 0px;
          }
          .btn {
            border-radius: 50px;
            background: #FFFFFF;
            box-shadow: 0px 6px 20px 0px #00000014;
            width : 140px;
            border: none;
            height : 48px;
            color: #282828;
            font-size: 14px;
            line-height: 48px;
            letter-spacing: 0px;
            text-align: center;
            margin-left:12px;
            margin-right:12px;
  
            &.hasIcon {
              line-height:19px;
              .iconfont {
                  display:block;
                  margin-top:4px;
              }
            }
            @media screen and (max-width: 450px) { // 手机宽度
              width: 40px !important;
              height: 44px !important;
              font-size: 10px !important;
            }
          }
      }


      }
    `,
    sideToolbarContainer: css`
      position:fixed;
      right:0;
      z-index:9;
      top:0px;
      transition: all 0.2s ease;
      &.is_3d_mode {
        top:180px;
      }
    `,
    schemeNameSpan: css`
    width:100%;
    font-size:16px;
    line-height:40px;
    text-align:center;
  `,
    viewSelectContainer: css`
      position: fixed;
      bottom: 0;
      right: 30%;
      transform: translateX(-70%);
      z-index: 9;
      .renderBtn {
        position: absolute;
        right: 50%;
        bottom: 10px;
        transform: translateX(50%);
      }
    `,
    selectBtns: css`
      position: absolute;
      right: 25%;
      top: 5px;
      display: flex;
      gap: 10px;
      z-index: 9;
    `,
    layoutPlusButton: css`
      position: absolute;
      top: 11px;
      right: 56px;
      display: flex;
      padding: 4px 12px;
      align-items: center;
      gap: 4px;
      border-radius: 50px;
      border: 1px solid #FFFFFF;
      background: #EDE5FF;
      height: 32px;
      z-index: 9;
      
      .text {
        color: #5C42FB;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    `
  }

});
