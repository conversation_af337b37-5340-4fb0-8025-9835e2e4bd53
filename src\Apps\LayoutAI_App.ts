import { APP_ID, checkIsMobile, GetAppId } from "@/config";
import { SensorsLogger } from "@/services/SensorsLogger";
import SunvegaAPI from "@api/clouddesign";

import { EventHandler, EventSystem } from "./EventSystem";
import { ILayoutAIConfigs, LayoutAI_Configs } from "./LayoutAI/Layout/TLayoutEntities/configures/LayoutAIConfigs";
import { OperationManager } from "./LayoutAI/OperationInfos/OperationManager";
import { BaseControls } from "./LayoutAI/Scene3D/controls/BaseControls";
import { OutlinePostProcess } from "./LayoutAI/Scene3D/builder/OutlinePostProcess";
import { AmbientLight, Camera, Group, Scene } from "three";
import { SceneLightMode } from "./LayoutAI/Scene3D/SceneMode";


export const LayoutAI_Version = "V0.5";
/**
 *  一些注册得默认命令
 */
export enum LayoutAI_Commands {
    OpenDwgFile = "OpenFile",
    OpenImitateImage = "OpenImitateImage",
    Undo = "Undo",
    Redo = "Redo",
    OpenSwjJsonFile = "OpenSwjJsonFile",
    SaveSwjJsonFile = "SaveSwjJsonFile",
    OpenSwjFileBySchemeId = "OpenSwjFileBySchemeId",
    OpenLayoutSchemeById = "OpenLayoutSchemeById",
    OpenSwjFileByBuildingId = "OpenSwjFileByBuildingId",
    SaveSwjJsonRoomTemplate = "SaveSwjJsonRoomTemplate",
    OpenSwjJsonRoomTemplate = "OpenSwjJsonRoomTemplate",
    ListMyLayoutSchemes = "ListMyLayoutSchemes",
    SaveMyLayoutSchemeAs = "SaveMyLayoutSchemeAs",
    SaveMyLayoutScheme = "SaveMyLayoutScheme",
    SaveRoomTemplates = "SaveRoomTemplates",
    SaveAsRoomTemplates = "SaveAsRoomTemplates",
    // 另存方案
    SaveSchemeAs = "SaveSchemeAs",

    ShowRawCadLayer = "ShowRawCadLayer",
    ShowAICadLayer = "ShowAICadLayer",
    ShowLayoutPlanLayer = "ShowLayoutPlanLayer",
    ShowCadFurnitureLayer = "ShowCadFurnitureLayer",
    ShowCadNameLayer = "ShowCadNameLayer",
    ShowOutLineLayer = "ShowOutLineLayer",
    LeaveSubHandler = "LeaveSubHandler",
    AcceptLeaveSubHandler = "AcceptLeaveSubHandler",

    DrawStraightWall = 'DrawStraightWall',
    DrawRectangleWall = 'DrawRectangleWall',
    DrawShapeWall = 'DrawShapeWall',
    DrawingBuildingSpace = 'DrawingBuildingSpace',

    DrawArcWall = 'DrawArcWall',
    SelectRoomArea = 'SelectRoomArea',

    MakeWallXml = "MakeWallXml",
    ApplyLayout = "ApplyLayout",
    DeleteRoomSeriesData = "DeleteRoomSeriesData",

    HideBackgroundGrids = "HideBackgroundGrids",
    ShowBackgroundGrids = "ShowBackgroundGrids",

    SwitchToSelectLayout = "SwitchToSelectLayout",
    SwitchToSelectSeriesSample = "SwitchToSelectSeriesSample",
    PerformFurnish = "PerformFurnish",
    PerformSave = "PerformSave",
    CreateSchemePerformFurnish = "CreateSchemePerformFurnish",
    OpenLayoutSchemeIn3D = "OpenLayoutSchemeIn3D",
    CreateAndOpenDreamerScheme = "CreateAndOpenDreamerScheme",
    RenderAndOpenPanorama = "RenderAndOpenPanorama",

    TransferCadFurnitureLayout = "TransferCadFurnitureLayout",

    MoveFurniture = "MoveFurniture",
    MoveWall = "MoveWall",
    MoveWinDoorHandler = "MoveWinDoorHandler",  // 移动门窗
    AllRoomLayout = "AllRoomLayout",
    AllRoomClear = "AllRoomClear",
    selectAllRoom = "selectAllRoom",

    AI_KitchenLayout = "AI_KitchenLayout",
    AI_RoomLayout = "AI_RoomLayout",

    Transform_Moving = "Transform_Moving",
    Transform_MovingWall = "Transform_MovingWall",
    Transform_MovingStruture = "Transform_MovingStruture",
    Transform_Scaling = "Transform_Scaling",
    Transform_Dimension = "Transform_Dimension",
    Transform_Combination = "Transform_Combination",
    Transform_Rotate = "Transform_Rotate",
    EditCeiling = "EditCeiling",  // 编辑吊顶

    AddFurniture = "AddFurniture",  // 添加
    DeleteFurniture = "DeleteFurniture",  // 删除
    RotateFurniture = "RotateFurniture", // 旋转
    TiltFurniture = "TiltFurniture", // 倾斜
    FlipFurniture = "FlipFurniture", // 镜像
    FlipFurnitureVertical = "FlipFurnitureVertical", // 上下镜像
    CopyFurniture = "CopyFurniture", // 复制
    ToAiCadMode = "ToAiCadMode", // 返回CAD模式
    Empty = "Empty", // 清空
    Reset = "Reset", //重置
    WholeHouseAILayout = "WholeHouseAILayout",
    SaveLayoutScheme = "SaveLayoutScheme",

    SplitWall = "SplitWall",
    ToElectricityMode = "ToElectricityMode",

    StartFurnishRemaining = "StartFurnishRemaining",
    FinishFurnishRemaining = "FinishFurnishRemaining",
    MoveCopyImageHandler = "MoveCopyImageHandler",
    RulerModeHandler = "RulerModeHandler",
    DeleteRuler = "DeleteRuler",
    EnterBaseGroupEdit = "EnterBaseGroupEdit",

    // AI拆改模式
    SelHouseStructure = "SelHouseStructure",
    RoomSpaceDesign = "RoomSpaceDesign",
    ShowRemodelingResult = "ShowRemodelingResult",
    OpenDwgFilefromWork = "OpenDwgFilefromWork",
    // 户型纠正模式
    EnterHouseCorrection = "EnterHouseCorrection",

    // 体验版本模式
    SelectHouse = "SelectHouse",
    SelectLayout = "SelectLayout",
    SelectSeries = "SelectSeries",
    TrialResultShow = "TrialResultShow",
    UploadHouse = "UploadHouse",

    //  后处理添加筒灯
    PostAddDownlights = "PostAddDownLights",
    QuerySpaceTemplates = "QuerySpaceTemplates"

}


export enum LayoutAI_CursorState {
    Default = "Default",
    Drawing = "Drawing",
    Moving = "Moving",
    Leftmove = "Leftmove",
    Rightmove = "Rightmove",
    Acrossmove = "Acrossmove",
    verticalmove = "verticalmove",
    Text = "Text",
    Pointer = "Pointer",
    SplitWall = "SplitWall",
}

export enum LayoutAI_Events {
    SeriesSampleSelected = "SeriesSampleSelected",
    ClickLayoutScheme = "ClickLayoutScheme",
    ClickWholeLayoutScheme = "ClickWholeLayoutScheme",
    ClearRoom2SeriesSample = "ClearRoom2SeriesSample",
    SelectedFurniture = "SelectedFurniture",
    SubmitLayoutIssueReport = "SubmitLayoutIssueReport",
    OpenIssueScheme = "OpenIssueScheme",
    OpenMyLayoutSchemeUrl = "OpenMyLayoutSchemeUrl",
    OpenMyLayoutSchemeData = "OpenMyLayoutSchemeData",
    OpenMyLayoutSchemeIn3D = "OpenMyLayoutSchemeIn3D",
    LoadSvjLayoutSchemeData = "LoadSvjLayoutSchemeData",
    OnloadedXmlLayoutScheme = "OnloadedXmlLayoutScheme",
    SaveLayoutScheme = "SaveLayoutScheme",
    SaveLayoutSchemeAs = "SaveLayoutSchemeAs",
    MoveFurniture = "MoveFurniture",
    AddFurniture = "AddFurniture",
    UpdateSize = "UpdateSize",
    HandleSwitchDrawingLayer = "HandleSwitchDrawingLayer",
    AIMatchingFigureSelected = 'AIMathcingFigureSelected',
    UpdateSideLayoutSchemeCandidates = "UpdateSideLayoutSchemeCandidates",
    AllFurnishRooms = 'AllFurnishRooms',
    ClearLayout = 'ClearLayout',
    ClearSeries = 'ClearSeries',
    /**
     *  Cad智能饰品
     */
    CadAutoDecorations = "CadAutoDecorations",

    CadKitchenLayout = "CadKitchenLayout",

    /**
     *  解除组合
     */
    HandleUnGroupTemplate = "HandleUnGroupTemplate",
    ReplaceMaterial = "ReplaceMaterial",

    ShowCurrentTestingDataset = "ShowCurrentTestingDataset",
    OnClickTestingHouseSchemeInfo = "OnClickTestingHouseSchemeInfo",
    OnChangeTestingHouseSchemeLabel = "OnChangeTestingHouseSchemeLabel",
    OnClickStartRuningTesting = "OnClickStartRuningTesting",
    ChangeSpaceName = "ChangeSpaceName",
    PostBuildingId = "PostBuildingId",
    SingleRoomLayout = "SingleRoomLayout",
    leaveSingleRoomLayout = "leaveSingleRoomLayout",
    CreateCombination = "CreateCombination",
    ReplaceEntity = "ReplaceEntity",
    DimensionInput = "DimensionInput",
    WinDoorDimensionInput = "WinDoorDimensionInput",  // 添加门窗尺寸输入事件
    orginDimensionRect = "orginDimensionRect",
    DimensionWall = "DimensionWall",
    Init = "Init",
    scale = "scale",

    PrepareRestartFurnishRemaining = "PrepareRestartFurnishRemaining",
    StartAutoFurnishRemaining = "StartAutoFurnishRemaining",
    CreateSchemePerformFurnish = "CreateSchemePerformFurnish",
    ApplyFurnishTo3D = "ApplyFurnishTo3D",
    CheckUnfurnishedBeforePerformFurnish = "CheckUnfurnishedBeforePerformFurnish",
    HandleLockCopyImage = "HandleLockCopyImage",
    MeasureScale = "MeasureScale",
    ConfirmScale = "ConfirmScale",
    ExitMeasureScale = "ExitMeasureScale",
    AutoRuler = "AutoRuler",
    UpdateCandidateRects = "UpdateCandidateRects",
    Trim = "Trim",
    updateGroup = "updateGroup",
    ShowLivingRoomSpace = "ShowLivingRoomSpace",

    Match3dPreviewMaterials = "Match3dPreviewMaterials",

    // 拆改模式
    AddRoomSpaceMark = "AddRoomSpaceMark",
    ApplyRemodelingScheme = "ApplyRemodelingScheme",
    autoSave = "autoSave",
    cleanSelect = "cleanSelect",

    // 户型纠正模式
    ApplyHouseCorrectionScheme = "ApplyHouseCorrectionScheme",
    selectRoomArea = "selectRoomArea",
    mobileAddFurniture = "mobileAddFurniture",
    showCustomKeyboard = "showCustomKeyboard",
    ResetSize = "ResetSize",
    updateLast_pos = "updateLast_pos",

    // 体验版本模式
    HouseRecognitionStatus = "HouseRecognitionStatus",

    // 加载临摹图文件
    LoadImitateImageFile = "LoadImitateImageFile",
    // 选中素材
    SelectedMaterial = "SelectedMaterial",
    //房间分区
    AddSubRoomArea = "AddSubRoomArea",
    UpdateRoomSubAreaType = "UpdateRoomSubAreaType",
    CopyRoomSubArea = "CopyRoomSubArea",
    // 绘制形状墙
    DrawShapeWall = "DrawShapeWall",
    SelectedSquareMaterial = "SelectedSquareMaterial",
    MaterialSquareDragup = "MaterialSquareDragup",
    UpdateFigureElement = "UpdateFigureElement",
    IsClickDrawPic = "IsClickDrawPic",

    DrawingBuildingSpace = "DrawingBuildingSpace",
    SplitBuildingSpace = "SplitBuildingSpace",
    setTopWallMenuProps = "setTopWallMenuProps"
}
const DefaultTranslate = (a: string) => a;

const getLayoutAIConfigs = () => {
    return LayoutAI_App.instance.Configs;
}

/**
 *   不依赖3D场景
 */
export interface I_Scene3D {
    isRendering(): boolean;
    isValid(): HTMLDivElement;
    bindMainDiv(div: HTMLDivElement): any;
    reset(): void;
    cleanWalls(): void;
    cleanWindows(): void;
    cleanFurnitures(): void;
    cleanRoomEntities(): void;
    updateShadowTexture(): void;
    setSelectionBox(ele: any): void;
    clearSelectionBox(): void;

    onScaleUp(t: number): void;
    makeDirty(t?: number): void;
    setLoadingFigureModelUUid(uuid: string, materialId: string): void;
    getLoadingFigureModelUUid(uuid: string): string;

    setLightGroupVisible(offline: boolean, test: boolean, rt: boolean): void;
    setLightMode(light_mode: SceneLightMode): void;
    getLightMode(): SceneLightMode;
    isNightMode(): boolean;

    get active_controls(): BaseControls;
    get outlinePostProcessing(): OutlinePostProcess;

    get aiLightsGroupTest(): Group;
    get dayLightsGroup(): Group;
    get camera(): Camera;
    get viewWidth(): number;
    get viewHeight(): number;
    get scene(): Scene;

    rootNode: any;
}

export interface I_Scene3DManager {
    readonly scene3D: I_Scene3D;

    initScene3D(force: boolean): void;
    updateScene3D(force?: boolean): void;
    UpdateScene3DWithMaterials(force?: boolean, options?: { needs_centered?: boolean }): Promise<void>;
    bindOnSelectFigure(func: (...args: any) => void): void;

    bindOnSelectRoom(func: (...args: any) => void): void;

    bindMainDiv(div: HTMLDivElement): any;

    onElementUpdate(ele: any, options?: { isWhite?: boolean, updateTexture?: boolean, [key: string]: any }): void;

}

export interface I_LayoutEntityContainer {

}

export interface RenderSubmitObject {
    drawPictureMode: string;
    radioMode: number;
    resolution: number;
}

export class LayoutAI_App extends OperationManager {
    private _app_name: string;
    static instance: LayoutAI_App;
    EventSystem: EventSystem;

    static apps_container: { [key: string]: LayoutAI_App } = {};
    static apps_generator: { [key: string]: () => LayoutAI_App } = {};
    protected _initialized: boolean;
    protected _prepared: boolean;
    protected _isSelecting: boolean;   //是否选中，用于展示右侧属性面板或者是小黑条
    /**
     *   0: 非debug; 1: debug;  2：载入测试数据，debug
     *   默认为0
     */
    _debug_mode: number;
    _haveData: boolean;

    _current_handler_mode: string = "";

    /**
     *  App路由来源
     */
    public _app_route: string = "";
    _temp_data: any;
    _attached_elements: { [key: string]: any };


    protected _scene3d_manager: I_Scene3DManager = null;
    /**
     *  创建3D场景管理器的方法, 可以动态注册
     */
    static createScene3dManager: () => I_Scene3DManager = null;

    /**
     *  是否平板翻转了
     */
    _is_landscape: boolean = false;


    /**
     *  是否网址带了  debug 标记
     */
    _is_website_debug: number = 0;
    private t: (t: string) => string;
    isOverSea: boolean;  //是否为海外用户
    renderSubmitObject: RenderSubmitObject;
    private _Configs: ILayoutAIConfigs;

    constructor(app_name: string = 'layout_ai') {
        super();

        this._Configs = {
            needs_adjust_ceiling_after_matching: true,
            default_is_auto_sub_area: false, prepare_auto_layout: true,
            default_mobile_device_pixel_ratio: 1.5, default_pc_device_pixel_ratio: 2, saving_localstorage_layout_scheme: true,
            update3d_when_xmlscheme_onloaded: false, is_auto_predict_roomname: false, is_drawing_ceiling_lines_in_2d: true,
            loading_figure_model_timeout: 10000,
            is_post_add_main_lights: true,
            mouse_state_to_update_selections: "OnMouseDown",
            isClickDrawPic: true,
            is_show_outline: false,

        };
        // 绑定全局配置函数 --- 后置绑定, 减少对LayoutAI_App的依赖
        LayoutAI_Configs.getConfigures = getLayoutAIConfigs;


        this.t = (a: string) => a; // 给一个默认的方法
        this._app_name = app_name;
        this._initialized = false;
        this._prepared = false;
        this._debug_mode = 0;
        this._haveData = false;
        LayoutAI_App.apps_container[app_name] = this;
        this.EventSystem = new EventSystem();
        this._current_handler_mode = "";
        this._isSelecting = false;
        (globalThis as any).LayoutAI_App = this;
        this.renderSubmitObject = {
            drawPictureMode: "aiDrawing",
            radioMode: 1,
            resolution: 1
        };


    }
    /**
     * 翻译函数
     * @param a 
     * @returns 
     */
    static get t() {
        if (LayoutAI_App.instance) {
            return LayoutAI_App.instance.t;
        }
        return DefaultTranslate;
    }
    static set t(tt: (s: string) => string) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.t = tt;
        }
    }
    get appName() {
        return this._app_name;
    }

    public get initialized() {
        return this._initialized;
    }
    public get isMoblie() {
        return checkIsMobile(); // this._is_mobile;
    }
    public get prepared() {
        return this._prepared;
    }
    public init() {
        this._initialized = true;
    }
    public bindCanvas(canvas: HTMLCanvasElement = null, sc_ratio: number = 1) {
    }
    get layout_container(): I_LayoutEntityContainer {
        return null;
    }
    get scene3D() {
        return this?.scene3DManager?.scene3D || null;
    }
    get scene3DManager() {
        if (!this._scene3d_manager) {
            if (LayoutAI_App.createScene3dManager) // 全局动态注册了这个函数
            {
                this._scene3d_manager = LayoutAI_App.createScene3dManager();
            }
        }
        return this._scene3d_manager;
    }

    bindScene3D(div: HTMLDivElement) {
        if (this._scene3d_manager) {
            this._scene3d_manager.bindMainDiv(div);
        }
    }
    public get Configs(): ILayoutAIConfigs {
        return this._Configs;
    }
    public set Configs(configs: ILayoutAIConfigs) {
        this._Configs = {...this._Configs, ...configs};
    }
    public adjustScaleByCanvas() {

    }

    public async prepare(): Promise<void> {
        this._prepared = true;
    }

    public update() {

    }

    public updateScene3D(force: boolean = false) {
        if (this._scene3d_manager) {
            this._scene3d_manager.updateScene3D(force);
        }
    }
    public setIsSelecting(value: boolean) {
        this._isSelecting = value;
    }
    public getIsSelecting() {
        return this._isSelecting;
    }
    static NewApp(app_name: string) {
        if (!LayoutAI_App.apps_container[app_name]) {
            if (LayoutAI_App.apps_generator[app_name]) {
                LayoutAI_App.apps_container[app_name] = LayoutAI_App.apps_generator[app_name]();
            }
        }
    }
    static GetApp(app_name: string) {
        return LayoutAI_App.apps_container[app_name];
    }
    static UseApp(app_name: string) {
        LayoutAI_App.instance = LayoutAI_App.apps_container[app_name];

    }

    static RegisterApp(app_name: string, generator: () => LayoutAI_App) {
        LayoutAI_App.apps_generator[app_name] = generator;
    }
    static RunCommand(cmd_name: string) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance._runCommand(cmd_name);
        }
    }
    static DispatchEvent(event_name: string, event_param: any) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance._dispatchEvent(event_name, event_param);
        }
    }

    static get IsDebug(): boolean {
        if (LayoutAI_App.instance) {
            return (LayoutAI_App.instance._debug_mode || LayoutAI_App.instance._is_website_debug) != 0;
        }
        return false;
    }

    static get IsMobile() {
        if (LayoutAI_App.instance) {
            return LayoutAI_App.instance.isMoblie;
        }
        return false;
    }

    static get HandlerMode(): string {
        if (LayoutAI_App.instance) {
            return LayoutAI_App.instance._current_handler_mode;
        }
        return "";
    }

    // 监听事件
    static on(eventName: string, handler: EventHandler) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance._on(eventName, handler);
        }
    }

    // 清除事件
    static off(eventName: string) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance._off(eventName);
        }
    }

    static emit(eventName: string, eventParam: any) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.EventSystem.emit(eventName, eventParam);
        }
    }
    // 多重监听事件
    static on_M(eventName: string, object_id: string = "", handler: EventHandler) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.EventSystem.on_M(eventName, object_id, handler);
        }
    }

    // 多重清除事件
    static off_M(eventName: string, object_id: string = "") {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.EventSystem.off_M(eventName, object_id);
        }
    }
    static off_M_All(options: { object_id?: string, event_name?: string }) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.EventSystem.off_M_All(options);
        }
    }

    static emit_M(eventName: string, eventParam: any) {
        if (LayoutAI_App.instance) {
            LayoutAI_App.instance.EventSystem.emit_M(eventName, eventParam);
        }
    }

    public static closeApp() {
        SensorsLogger.trackAppCloseEvent("closeByPlugin");
        let appId = GetAppId() || APP_ID;
        console.log("Close plugin app " + appId);
        SunvegaAPI.Platform.Application.closeApp({ appId: appId });
    }

    public static hideApp() {
        let appId = GetAppId() || APP_ID;
        SunvegaAPI.Platform.Application.hideApp({ appId: appId });
    }

    public fireEvent() {

    }

    public _runCommand(cmd_name: string) {

    }

    public _dispatchEvent(event_name: string, event_param: any) {

    }

    _off(eventName: string): void {
        this.EventSystem.off(eventName);
    }

    _on(eventName: string, handler: EventHandler): void {
        this.EventSystem.on(eventName, handler);
    }

    // 便于子类重写
    public getPainterGlobalAlpha(): number {
        return 0.5;
    }


}