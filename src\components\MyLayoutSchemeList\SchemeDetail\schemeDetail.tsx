import { PanelContainer } from "@svg/antd-cloud-design";
import useStyles from "./style"
import { observer } from "mobx-react-lite";
import { t } from "i18next";
import { Button, message } from "@svg/antd";
import { useState } from "react";

interface Props {
    close: () => void;
    schemeItem: any
}

const SchemeDetail: React.FC<Props> = ({close, schemeItem}) => {
    const { styles } = useStyles();
    const [showIdTip, setShowIdTip] = useState(false);
    console.log('zzz', schemeItem)

    const copyToClipboard = async (text: string) => {
        try {
            if (navigator.clipboard) {
                await navigator.clipboard.writeText(text);
                message.success('已复制到剪贴板');
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                message.success('已复制到剪贴板');
            }
        } catch (err) {
            message.error('复制失败');
        }
    };

    const handleIdClick = () => {
        setShowIdTip(true);
        setTimeout(() => setShowIdTip(false), 2000);
    };

    const infoList = [
        { key: 'id', label: '方案ID', value: schemeItem?.id, copy: true },
        { key: 'address', label: '小区', value: schemeItem?.address },
        { key: 'svjSchemeId', label: '户型', value: schemeItem?.svjSchemeId },
        { key: 'area', label: '使用面积', value: schemeItem?.area ? `${schemeItem.area}m²` : '/' },
        { key: 'orientation', label: '朝向', value: schemeItem?.orientation },
        { key: 'cntactMan', label: '姓名', value: schemeItem?.cntactMan },
        { key: 'mobile', label: '手机号', value: schemeItem?.mobile },
        { key: 'panoLink', label: '全景', value: schemeItem?.panoLink ? <a href={schemeItem.panoLink} target="_blank" rel="noopener noreferrer">{schemeItem.panoLink}</a> : '/' },
        { key: 'funcRequire', label: '需求标签', value: schemeItem?.funcRequire },
        { key: 'updateDate', label: '最后修改', value: schemeItem?.updateDate },
    ];

    const editForm = () => {

    }

    const gotoDesign = () => {
        
    }

    return (
        <PanelContainer
            className={styles.panel}
            title={t('方案详情')}
            center={true}
            width={979}
            height={700}
            onClose={close}
            draggable={true}
        >
            <div>
                <div className={styles.contain}>
                    <div className={styles.left}>
                        <img src={schemeItem?.coverImage} alt="" />
                    </div>
                    
                    <div className={styles.right}>
                        <span className={styles.name}>{schemeItem?.layoutSchemeName}</span>
                        <div className={styles.infoGrid}>
                        {infoList.map((item, idx) => (
                            <div className={styles.infoItem} key={idx}>
                                <span className={styles.label}>{item.label}：</span>
                                <span
                                    className={
                                        item.key === 'id'
                                            ? styles.idValue
                                            : styles.value
                                    }
                                    style={item.copy ? { cursor: 'pointer', userSelect: 'none' } : {}}
                                    title={item.copy ? "双击复制" : undefined}
                                    onDoubleClick={item.copy && item.value ? () => copyToClipboard(item.value) : undefined}
                                >
                                    {item.value || '/'}
                                </span>
                            </div>
                        ))}
                        </div>

                        <div className={styles.btnContain}>
                        <Button type="primary" className={styles.btnDesign} onClick={editForm}>编辑方案信息</Button>
                        <Button type="primary" className={styles.btnDesign} onClick={gotoDesign}>
                            去设计
                        </Button>
                        </div>
                    </div>
                    
                </div>

                <div>标准渲染和AI绘图</div>
            </div>
        </PanelContainer>
    )
}

export default observer(SchemeDetail)