import { createStyles } from '@svg/antd/es/theme/utils';

export default createStyles(({ css }) => {
  return {
    leftPanelRoot: css`
      position: fixed;
      background: #fff;
      z-index: 10;
      transition: all 0.3s ease-in-out;
      .closeBtn {
        display: none;
        position: absolute;
        right: 6px;
        top: 6px;
        font-size: 20px;
        width: 60px;
        height: 24px;
        text-align: right;
      }
      &.panel_hide {
        box-shadow: 0px 0px 0px 0px #00000000;
      }
      @media screen and (orientation: landscape) {
        position: fixed;
        left: 12px;
        top: 52px;
        bottom: 12px;
        right: auto;
        height: auto;
        padding-left: 0 !important;
        max-height: calc(var(--vh, 1vh) * 100);
        max-width: 224px;
        width: 224px;
        border-radius: 8px;
        box-shadow: 0px 0px 16px 10px #0000000A;
        &.panel_hide {
          transform: translateX(calc(-100% - 12px));
        }
      }
      @media screen and (orientation: portrait) {
        position: fixed;
        left: 0;
        bottom: 0px;
        right: 0;
        width: auto;
        height: 340px;
        max-width: auto;
        max-height: 340px;
        overflow: hidden;
        background-color: #fff;
        border-radius: 8px 8px 0px 0px;
        box-shadow: 0px 0px 16px 10px #0000000A;
        &.panel_hide {
          transform: translateY(100%);
        }
        .closeBtn {
          display: block;
        }
      }
    `,
    tabBar: css`
      height: 60px;
      width: 100%;
      @media screen and (orientation: landscape) {
        height: 55px;
        width: 100%;
        color: #000;
        font-weight: 700;
        padding: 12px 13px 0;
      }
      @media screen and (orientation: portrait) {
        width: 30%;
        margin: 10px;
        height: auto;
      }
    `,
    collapseBtn: css`
      display: none;
      width: 20px;
      height: 48px;
      line-height: 48px;
      text-align: center;
      background-color: #fff;
      border-radius: 0px 6px 6px 0px;
      box-shadow: 0px -16px 16px 0px #00000005;
      cursor: pointer;
      transition: all 0.3s ease-in-out;

      @media screen and (orientation: landscape) {
        display: block;
        position: fixed;
        left: 235px;
        top: calc(50% - 48px);
        z-index: 9;
      }
      @media screen and (orientation: portrait) {
        position: fixed;
        bottom: 120px;
        left: 0px;
        z-index: 999;
      }
      &.panel_hide {
        left: 0px;
        display: block;
      }
    `,
    popupContainer: css`
      padding: 0 12px;
      height: calc(100% - 60px);
      overflow: hidden;
    `,
    itemGrid: css`
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      padding: 4px 0;
      overflow-y: auto;
      scrollbar-width: none;  /* Firefox */
      -ms-overflow-style: none;  /* IE and Edge */
      &::-webkit-scrollbar {
        display: none;  /* Chrome, Safari and Opera */
      }
    `,
    gridItem: css`
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 6px 4px;
      cursor: pointer;
      transition: all 0.3s;
      user-select: none;
    `,
    itemIcon: css`
      font-size: 50px;
      height: 50px;
      width: 70px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      background: #F5F5F5;
      -webkit-user-drag: none;
      -khtml-user-drag: none;
      -moz-user-drag: none;
      -o-user-drag: none;
      user-drag: none;
      
      img {
        max-width: 50px;
        max-height: 40px;
        object-fit: contain;
        -webkit-user-drag: none;
        -khtml-user-drag: none;
        -moz-user-drag: none;
        -o-user-drag: none;
        user-drag: none;
        pointer-events: none;
      }
    `,
    itemLabel: css`
      font-size: 14px;
      color: #333;
      height: 30px;
      line-height: 30px;
      text-align: center;
    `
  };
});