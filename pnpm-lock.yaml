lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@ant-design/cssinjs':
        specifier: ^1.21.0
        version: 1.23.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@ant-design/icons':
        specifier: ^5.4.0
        version: 5.6.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@ant-design/pro-components':
        specifier: ^2.6.28
        version: 2.8.7(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(rc-field-form@2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@api/clouddesign':
        specifier: 1.15.48
        version: 1.15.48
      '@clouddesign/design-designmaterial':
        specifier: ^1.7.8
        version: 1.7.8(@svg/request@0.4.0(axios@0.27.2))(axios@0.27.2)
      '@clouddesign/design-framework':
        specifier: 1.0.1
        version: 1.0.1
      '@clouddesign/royscene_threejs_extend':
        specifier: 0.0.142
        version: 0.0.142(@clouddesign/design-designmaterial@1.7.8(@svg/request@0.4.0(axios@0.27.2))(axios@0.27.2))(@clouddesign/design-framework@1.0.1)(@sd/royprotocal@3.0.4-0)(@svg/oss-upload@2.2.0(@types/express@4.17.21))(@svg/request@0.4.0(axios@0.27.2))(axios@0.27.2)(base64-js@1.5.1)(buffer@6.0.3)(fflate@0.8.2)(js-cookie@3.0.5)(spark-md5@3.0.2)(three@0.171.0)
      '@layoutai/basic_data':
        specifier: ^1.0.2
        version: 1.0.2(@layoutai/z_polygon@0.0.2)
      '@layoutai/model3d_api':
        specifier: ^1.0.7
        version: 1.0.7(@layoutai/z_polygon@0.0.2)(tough-cookie@5.1.2)
      '@layoutai/z_polygon':
        specifier: ^0.0.2
        version: 0.0.2
      '@sd/plugin_svgsocketclient':
        specifier: 3.0.12-0
        version: 3.0.12-0(@sd/roybase@3.0.38-0)(@sd/royframebase@3.0.20-0(@sd/roybase@3.0.38-0)(@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0))(@sd/royprotocal@3.0.4-0))(@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0))(@sd/roysvgapi@3.0.24-0)
      '@sd/roybase':
        specifier: ^3.0.38-0
        version: 3.0.38-0
      '@sd/roysvgapi':
        specifier: ^3.0.24-0
        version: 3.0.24-0
      '@stagewise-plugins/react':
        specifier: ^0.4.7
        version: 0.4.7(@stagewise/toolbar@0.4.6)
      '@stagewise/toolbar-react':
        specifier: ^0.4.6
        version: 0.4.6(@types/react@18.2.55)(react@18.2.0)
      '@svg/antd':
        specifier: 2.6.3
        version: 2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/antd-basic':
        specifier: ^5.6.8
        version: 5.6.8(@svg/antd@2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(@types/express@4.17.21)(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(rc-field-form@2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/antd-cloud-design':
        specifier: 4.17.0
        version: 4.17.0(@svg/antd@2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(@svg/request@0.4.0(axios@0.27.2))(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/deploy':
        specifier: ^2.1.0
        version: 2.1.0
      '@svg/lang':
        specifier: ^2.7.1
        version: 2.9.2
      '@svg/oss-upload':
        specifier: ^2.0.3
        version: 2.2.0(@types/express@4.17.21)
      '@svg/request':
        specifier: ^0.4.0
        version: 0.4.0(axios@0.27.2)
      '@svg/sensors':
        specifier: ^2.5.1
        version: 2.5.2
      '@svg/sso-plus':
        specifier: ^1.0.68
        version: 1.1.0
      '@types/css-modules':
        specifier: ^1.0.5
        version: 1.0.5
      axios:
        specifier: 0.27.2
        version: 0.27.2(debug@4.4.0)
      axios-cookiejar-support:
        specifier: ^5.0.5
        version: 5.0.5(axios@0.27.2)(tough-cookie@5.1.2)
      base64-js:
        specifier: ^1.5.1
        version: 1.5.1
      buffer:
        specifier: ^6.0.3
        version: 6.0.3
      classnames:
        specifier: ^2.5.1
        version: 2.5.1
      default-passive-events:
        specifier: ^2.0.0
        version: 2.0.0
      express:
        specifier: ^4.21.2
        version: 4.21.2
      fflate:
        specifier: ^0.8.2
        version: 0.8.2
      form-render:
        specifier: ^2.4.5
        version: 2.5.2(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      http:
        specifier: 0.0.1-security
        version: 0.0.1-security
      i18next:
        specifier: 21.9.1
        version: 21.9.1
      i18next-browser-languagedetector:
        specifier: ^8.0.0
        version: 8.0.5
      i18next-http-backend:
        specifier: ^2.5.2
        version: 2.7.3
      i18next-icu:
        specifier: ^2.3.0
        version: 2.3.0(intl-messageformat@10.7.16)
      intl-messageformat:
        specifier: ^10.7.14
        version: 10.7.16
      js-base64:
        specifier: 3.6.0
        version: 3.6.0
      js-cookie:
        specifier: ^3.0.5
        version: 3.0.5
      lil-gui:
        specifier: ^0.20.0
        version: 0.20.0
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      mobx:
        specifier: 6.6.1
        version: 6.6.1
      mobx-react:
        specifier: 7.5.2
        version: 7.5.2(mobx@6.6.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      mobx-react-lite:
        specifier: ^4.0.7
        version: 4.1.0(mobx@6.6.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      node-polyfill-webpack-plugin:
        specifier: 2.0.1
        version: 2.0.1(webpack@5.99.6)
      pptxgenjs:
        specifier: ^3.12.0
        version: 3.12.0
      qs:
        specifier: ^6.12.3
        version: 6.14.0
      react:
        specifier: 18.2.0
        version: 18.2.0
      react-device-detect:
        specifier: ^2.0.1
        version: 2.2.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-dom:
        specifier: 18.2.0
        version: 18.2.0(react@18.2.0)
      react-draggable:
        specifier: ^4.4.6
        version: 4.4.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-i18next:
        specifier: 11.18.4
        version: 11.18.4(i18next@21.9.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-if:
        specifier: ^4.1.6
        version: 4.1.6(react@18.2.0)
      react-router-dom:
        specifier: 6.3.0
        version: 6.3.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-transition-group:
        specifier: ^4.4.5
        version: 4.4.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      recharts:
        specifier: 2.9.2
        version: 2.9.2(prop-types@15.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      spark-md5:
        specifier: ^3.0.2
        version: 3.0.2
      three:
        specifier: ^0.171.0
        version: 0.171.0
      ws:
        specifier: ^8.18.0
        version: 8.18.1
    devDependencies:
      '@babel/core': 7.23.0
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/runtime': 7.23.0
      '@babel/types': 7.23.0
      '@efox/emp': 2.7.1_c7124a5fb2a5edc58908b0d01b2fc959
      '@efox/emp-compile-swc': 1.3.3
      '@efox/emp-tsconfig': 1.1.4_typescript@5.3.3
      '@efox/eslint-config-react-prittier-ts': 1.2.6_typescript@5.3.3
      '@eslint/js': 9.31.0
      '@playwright/test': 1.54.1
      '@types/js-cookie': 3.0.6
      '@types/lodash': 4.17.20
      '@types/node': 20.19.9
      '@types/react': 18.2.55
      '@types/react-dom': 18.2.19
      '@types/react-router-dom': 5.3.3
      '@types/spark-md5': 3.0.5
      '@types/three': 0.171.0
      browserslist: 4.0.0
      concurrently: 8.2.2
      copy-webpack-plugin: 11.0.0_webpack@5.100.2
      cross-env: 7.0.3
      eslint: 9.31.0
      react-app-polyfill: 2.0.0
      ts-loader: 9.5.2_typescript@5.3.3+webpack@5.100.2
      typescript: 5.3.3
      typescript-eslint: 8.38.0_eslint@9.31.0+typescript@5.3.3
      webpack: 5.100.2_webpack-cli@5.1.4
      webpack-cli: 5.1.4_webpack@5.100.2

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@ant-design/colors@6.0.0':
    resolution: {integrity: sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==}

  '@ant-design/colors@7.2.0':
    resolution: {integrity: sha512-bjTObSnZ9C/O8MB/B4OUtd/q9COomuJAR2SYfhxLyHvCKn4EKwCN3e+fWGMo7H5InAyV0wL17jdE9ALrdOW/6A==}

  '@ant-design/cssinjs-utils@1.1.3':
    resolution: {integrity: sha512-nOoQMLW1l+xR1Co8NFVYiP8pZp3VjIIzqV6D6ShYF2ljtdwWJn5WSsH+7kvCktXL/yhEtWURKOfH5Xz/gzlwsg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'

  '@ant-design/cssinjs@1.23.0':
    resolution: {integrity: sha512-7GAg9bD/iC9ikWatU9ym+P9ugJhi/WbsTWzcKN6T4gU0aehsprtke1UAaaSxxkjjmkJb3llet/rbUSLPgwlY4w==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@ant-design/fast-color@2.0.6':
    resolution: {integrity: sha512-y2217gk4NqL35giHl72o6Zzqji9O7vHh9YmhUVkPtAOpoTCH4uWxo/pr4VE8t0+ChEPs0qo4eJRC5Q1eXWo3vA==}
    engines: {node: '>=8.x'}

  '@ant-design/icons-svg@4.4.2':
    resolution: {integrity: sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA==}

  '@ant-design/icons@4.8.3':
    resolution: {integrity: sha512-HGlIQZzrEbAhpJR6+IGdzfbPym94Owr6JZkJ2QCCnOkPVIWMO2xgIVcOKnl8YcpijIo39V7l2qQL5fmtw56cMw==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@ant-design/icons@5.6.1':
    resolution: {integrity: sha512-0/xS39c91WjPAZOWsvi1//zjx6kAp4kxWwctR6kuU6p133w8RU0D2dSCvZC19uQyharg/sAvYxGYWl01BbZZfg==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'

  '@ant-design/pro-card@2.9.7':
    resolution: {integrity: sha512-uDDYowmYH1ldRfG8Mb4QOwcEEz6ptRBQDLO1tkVADCRkdOMwz82xlZneR4uVuFyKcuNmgHzarYNncozBKhFuaA==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'

  '@ant-design/pro-components@2.8.7':
    resolution: {integrity: sha512-QhibkPsUJryEjI1QmwUn+XCngGHidu0ekvricL6TIEvPgP+AUAca29XutN5+Mmn8Xfja1ca9HFTHTgFoV74Z7Q==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-descriptions@2.6.7':
    resolution: {integrity: sha512-fgn2d0kDWUODGDWKpgziZuuqPlmIoKxQFJY9Yg4nbaRp8GDDKZeSSqgvW+OxjpYM8dxq31fiz1dZlZnOPoYKpg==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'

  '@ant-design/pro-field@3.0.4':
    resolution: {integrity: sha512-nJSng/6/pPZFdiFeTtZcBQLNrHg9tIeiKFR1+zzbnQbI3qBOFP9aBZS/+LwkQZcI2G71vrRgz2x5OhHb7AX0wQ==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'

  '@ant-design/pro-form@2.31.7':
    resolution: {integrity: sha512-0TCtIC/ynbLPoes8sLBFwFbi0tkeNmSU6the2EcyKIKDLfWHDbfkLM1OSFrzv3QD+H8OgFWMkTSOjhMOKSsOBg==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      rc-field-form: '>=1.22.0'
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-layout@7.22.4':
    resolution: {integrity: sha512-X2WO4L2itXemX4zhS+0NG+8kXQD5SX9sG+zjx/15BmIO3FvsUGqOHgoCg0vhd424EiyPj7WtdMZJ39G1xdgDwA==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-list@2.6.7':
    resolution: {integrity: sha512-6k/En7pioMgepho/1HMf2DAnkSTZiat1lDg2ggCok2lhSgqXzir7x22ewJQRgPvEiVb6/qqaFQNd7a8dnrFj1w==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-provider@2.15.4':
    resolution: {integrity: sha512-DBX0JNUNOYXAucVqd/zTdqtXckCDqr2Lo85KIku2YzWdhptDPDZRTNqL04JShjGejDl8fzwQ8yREHgVUfzn6Gg==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-skeleton@2.2.1':
    resolution: {integrity: sha512-3M2jNOZQZWEDR8pheY00OkHREfb0rquvFZLCa6DypGmiksiuuYuR9Y4iA82ZF+mva2FmpHekdwbje/GpbxqBeg==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-table@3.19.0':
    resolution: {integrity: sha512-nL25734d5q5oqtmG7Apn2TNJUnJE8m9dkopXMQdoNZnv8qeRQLBH+i5cZT1yh7FIO8z6QLXleg+KnR/cI7VRRw==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      rc-field-form: '>=1.22.0'
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/pro-utils@2.17.0':
    resolution: {integrity: sha512-hHKUISjMEoS+E5ltJWyvNTrlEA3IimZNxtDrEhorRIbgVYAlmEN5Mj/ESSofzDM3+UlxiI5+A/Y6IHkByTfDEA==}
    peerDependencies:
      antd: ^4.24.15 || ^5.11.2
      react: '>=17.0.0'
      react-dom: '>=17.0.0'

  '@ant-design/react-slick@1.1.2':
    resolution: {integrity: sha512-EzlvzE6xQUBrZuuhSAFTdsr4P2bBBHGZwKFemEfq8gIGyIQCxalYfZW/T2ORbtQx5rU69o+WycP3exY/7T1hGA==}
    peerDependencies:
      react: '>=16.9.0'

  '@api/clouddesign@1.15.48':
    resolution: {integrity: sha1-mkEBke+x1JoFO7kyQL3uZ2QNDIg=}
    engines: {node: '>=16.14.0'}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.23.0':
    resolution: {integrity: sha512-97z/ju/Jy1rZmDxybphrBuI+jtJjFVoz7Mr9yUQVVVi+DNZE333uFQeMOqcCIy1x3WYBIbWftUSLmbNXNT7qFQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.0':
    resolution: {integrity: sha512-VybsKvpiN1gU1sdMZIp7FcqphVVKEwcuj02x73uvcHE0PTihx1nlBcowYWhDwjpoAXRv43+gDzyggGnn1XZhVw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.0':
    resolution: {integrity: sha512-LVk7fbXml0H2xH34dFzKQ7TDZ2G4/rVTOrq9V+icbbadjbVxxeFeDsNHv2SrZeWoA+6ZiTyWYWtScEIW07EAcA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.27.0':
    resolution: {integrity: sha512-vSGCvMecvFCd/BdpGlhpXYNhhC4ccxyvQWpbGL4CWbvfEoLFWUZuSuf7s9Aw70flgQF+6vptvgK2IfOnKlRmBg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.27.0':
    resolution: {integrity: sha512-fO8l08T76v48BhpNRW/nQ0MxfnSdoSKUJBMjubOAYffsVuGG5qOfMq7N6Es7UJvi7Y8goXXo07EfcHZXDPuELQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.4':
    resolution: {integrity: sha512-jljfR1rGnXXNWnmQg2K3+bvhkxB51Rl32QRaOTuwwjviGrHzIbSc8+x9CpraDtbT7mfyjXObULP4w/adunNwAw==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.22.5':
    resolution: {integrity: sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.25.9':
    resolution: {integrity: sha512-IZtukuUeBbhgOcaW2s06OXTzVNJR0ybm4W5xC1opWFFJMZbwRj5LCk+ByYH7WdZPZTt8KnFwA8pvjN2yqcPlgw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.26.5':
    resolution: {integrity: sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.25.9':
    resolution: {integrity: sha512-ETzz9UTjQSTmw39GboatdymDq4XIQbR8ySgVrylRhPOFpsd+JrKHIuF0de7GCWmem+T4uC5z7EZguod7Wj4A4g==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.0':
    resolution: {integrity: sha512-U5eyP/CTFPuNE3qk+WZMxFkp/4zUzdceQlfzf7DdGdhp+Fezd7HD+i8Y24ZuTMKX3wQBld449jijbGq6OdGNQg==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.0':
    resolution: {integrity: sha512-iaepho73/2Pz7w2eMS0Q5f83+0RKI7i4xmiYeBmDzfRVbQtTOG7Ts0S4HzJVsTMGI9keU8rNfuZr8DKfSt7Yyg==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9':
    resolution: {integrity: sha512-ZkRyVkThtxQ/J6nv3JFYv1RYY+JT5BvU0y3k5bWrmuG4woXypRa4PXmm9RhOwodRkYFWqC0C0cqcJ4OqR7kW+g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9':
    resolution: {integrity: sha512-MrGRLZxLD/Zjj0gdU15dfs+HH/OXvnw/U4jJD8vpcP2CJQapPEv1IWwjc/qMg7ItBlPwSv1hRBbb7LeuANdcnw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9':
    resolution: {integrity: sha512-2qUwwfAFpJLZqxd02YW9btUCZHl+RFvdDkNfZwaIJrvB8Tesjsk8pEQkTvGwZXLqXUx/2oyY3ySRhm6HOXuCug==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9':
    resolution: {integrity: sha512-6xWgLZTJXwilVjlnV7ospI3xi+sl8lN8rXXbBD6vYn3UYDlGsag8wrZkKcSI8G6KgqKP7vNFaDgeDnfAABq61g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9':
    resolution: {integrity: sha512-aLnMXYPnzwwqhYSCyXfKkIkYgJ8zv9RK+roo9DkTXz38ynIhd9XCbN08s3MGvqL2MYGVUGdRQLL/JqBIeJhJBg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-class-properties@7.18.6':
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-decorators@7.25.9':
    resolution: {integrity: sha512-smkNLL/O1ezy9Nhy4CNosc4Va+1wo5w4gzSZeLe6y6dM4mmHfYOCPolXQPHQxonZCF+ZyebxN9vqOolkYrSn5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-numeric-separator@7.18.6':
    resolution: {integrity: sha512-ozlZFogPqoLm8WBr5Z8UckIoE4YQ5KESVcNudyXOR8uqIkliTEgJ3RoketfG6pmzLdeZF0H/wjE9/cCEitBl7Q==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-numeric-separator instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-methods@7.18.6':
    resolution: {integrity: sha512-nutsvktDItsNn4rpGItSNV2sz1XwS+nfU0Rg8aCx3W3NOKVzdMjJRu0O5OkgDp3ZGICSTbgRpxZoWsxoKRvbeA==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-methods instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.11':
    resolution: {integrity: sha512-0QZ8qP/***********************************************************+vzwTAg/sMWVNeWeNyaw==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-private-property-in-object instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-decorators@7.25.9':
    resolution: {integrity: sha512-ryzI0McXUPJnRCvMo4lumIKZUzhYUO/ScI+Mz4YVaTLt04DHNSjEUjKVvbzQjZFLuod/cYEc07mJWhzl6v4DPg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.26.0':
    resolution: {integrity: sha512-QCWT5Hh830hK5EQa7XzuqIkQU9tT/whqbDz7kuaZMHFl1inRRg7JnuAEOQ0Ur0QUl0NufCk1msK2BeY79Aj/eg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.26.0':
    resolution: {integrity: sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.25.9':
    resolution: {integrity: sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.25.9':
    resolution: {integrity: sha512-6jmooXYIwn9ca5/RylZADJ+EnSxVUS5sjeJ9UPk6RWRzXCmOJCy6dqItPJFpw2cuCangPK4OYr5uhGKcmrm5Qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.26.8':
    resolution: {integrity: sha512-He9Ej2X7tNf2zdKMAGOsmg2MrFc+hfoAhd3po4cWfo/NWjzEAKa0oQruj1ROVUdl0e6fb6/kE/G3SSxE0lRJOg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.25.9':
    resolution: {integrity: sha512-NT7Ejn7Z/LjUH0Gv5KsBCxh7BH3fbLTV0ptHvpeMvrt3cPThHfJfst9Wrb7S8EvJ7vRTFI7z+VAvFVEQn/m5zQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.26.5':
    resolution: {integrity: sha512-chuTSY+hq09+/f5lMj8ZSYgCFpppV2CbYrhNFJ1BFoXpiWPnnAb7R0MqrafCpN8E1+YRrtM1MXZHJdIx8B6rMQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.27.0':
    resolution: {integrity: sha512-u1jGphZ8uDI2Pj/HJj6YQ6XQLZCNjOlprjxB5SVz6rq2T6SwAR+CdrWK0CP7F+9rDVMXdB0+r6Am5G5aobOjAQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.25.9':
    resolution: {integrity: sha512-bbMAII8GRSkcd0h0b4X+36GksxuheLFjP65ul9w6C3KgAamI3JqErNgSrosX6ZPj+Mpim5VvEbawXxJCyEUV3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.26.0':
    resolution: {integrity: sha512-6J2APTs7BDDm+UMqP1useWqhcRAXo0WIoVj26N7kPFB6S73Lgvyka4KTZYIxtgYXiN5HTyRObA72N2iu628iTQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.25.9':
    resolution: {integrity: sha512-mD8APIXmseE7oZvZgGABDyM34GUmK45Um2TXiBUt7PnuAxrgoSVf123qUzPxEr/+/BHrRn5NMZCdE2m/1F8DGg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.25.9':
    resolution: {integrity: sha512-HnBegGqXZR12xbcTHlJ9HGxw1OniltT26J5YpfruGqtUHlz/xKf/G2ak9e+t0rVqrjXa9WOhvYPz1ERfMj23AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.25.9':
    resolution: {integrity: sha512-WkCGb/3ZxXepmMiX101nnGiU+1CAdut8oHyEOHxkKuS1qKpU2SMXE2uSvfz8PBuLd49V6LEsbtyPhWC7fnkgvQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.25.9':
    resolution: {integrity: sha512-t7ZQ7g5trIgSRYhI9pIJtRl64KHotutUJsh4Eze5l7olJv+mRSg4/MmbZ0tv1eeqRbdvo/+trvJD/Oc5DmW2cA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.25.9':
    resolution: {integrity: sha512-LZxhJ6dvBb/f3x8xwWIuyiAHy56nrRG3PeYTpBkkzkYRRQ6tJLu68lEF5VIqMUZiAV7a8+Tb78nEoMCMcqjXBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha512-0UfuJS0EsXbRvKnwcLjFtJy/Sxc5J5jhLHnFhy7u4zih97Hz6tJkLU+O+FMMrNZrosUPxDi6sYxJ/EA8jDiAog==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.25.9':
    resolution: {integrity: sha512-GCggjexbmSLaFhqsojeugBpeaRIgWNTcgKVq/0qIteFEqY2A+b9QidYadrWlnbWQUrW5fn+mCvf3tr7OeBFTyg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.26.3':
    resolution: {integrity: sha512-7CAHcQ58z2chuXPWblnn1K6rLDnDWieghSOEmqQsrBenH0P9InCUtOJYD89pvngljmZlJcz3fcmgYsXFNGa1ZQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.25.9':
    resolution: {integrity: sha512-2NsEz+CxzJIVOPx2o9UsW1rXLqtChtLoVnwYHHiB04wS5sgn7mrV45fWMBX0Kk+ub9uXytVYfNP2HjbVbCB3Ww==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.26.9':
    resolution: {integrity: sha512-Hry8AusVm8LW5BVFgiyUReuoGzPUpdHQQqJY5bZnbbf+ngOHWuCuYFKw/BqaaWlvEUrF91HMhDtEaI1hZzNbLg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.25.9':
    resolution: {integrity: sha512-8lP+Yxjv14Vc5MuWBpJsoUCd3hD6V9DgBon2FVYL4jJgbnVQ9fTgYmonchzZJOVNgzEgbxp4OwAf6xz6M/14XA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.25.9':
    resolution: {integrity: sha512-xoTMk0WXceiiIvsaquQQUaLLXSW1KJ159KP87VilruQm0LNNGxWzahxSS6T6i4Zg3ezp4vA4zuwiNUR53qmQAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.25.9':
    resolution: {integrity: sha512-9N7+2lFziW8W9pBl2TzaNht3+pgMIRP74zizeCSrtnSKVdUl8mAjjOP2OOVQAfZ881P2cNjDj1uAMEdeD50nuQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.25.9':
    resolution: {integrity: sha512-wI4wRAzGko551Y8eVf6iOY9EouIDTtPb0ByZx+ktDGHwv6bHFimrgJM/2T021txPZ2s4c7bqvHbd+vXG6K948Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.25.9':
    resolution: {integrity: sha512-PYazBVfofCQkkMzh2P6IdIUaCEWni3iYEerAsRWuVd8+jlM1S9S9cz1dF9hIzyoZ8IA3+OwVYIp9v9e+GbgZhA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.25.9':
    resolution: {integrity: sha512-g5T11tnI36jVClQlMlt4qKDLlWnG5pP9CSM4GhdRciTNMRgkfpo5cR6b4rGIOYPgRRuFAvwjPQ/Yk+ql4dyhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.26.3':
    resolution: {integrity: sha512-MgR55l4q9KddUDITEzEFYn5ZsGDXMSsU9E+kh7fjRXTIC3RHqfCo8RPRbyReYJh44HQ/yomFkqbOFohXvDCiIQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.25.9':
    resolution: {integrity: sha512-hyss7iIlH/zLHaehT+xwiymtPOpsiwIIRlCAOwBB04ta5Tt+lNItADdlXw3jAWZ96VJ2jlhl/c+PNIQPKNfvcA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.25.9':
    resolution: {integrity: sha512-bS9MVObUgE7ww36HEfwe6g9WakQ0KF07mQF74uuXdkoziUPfKyu/nIm663kz//e5O1nPInPFx36z7WJmJ4yNEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha512-oqB6WHdKTGl3q/ItQhpLSnWWOpjUJLsOCLVyeFgeTktkBSCiurvPOsyt93gibI9CmuKvTUEtWmG5VhZD+5T/KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.25.9':
    resolution: {integrity: sha512-U/3p8X1yCSoKyUj2eOBIx3FOn6pElFOKvAAGf8HTtItuPyB+ZeOqfn+mvTtg9ZlOAjsPdK3ayQEjqHjU/yLeVQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6':
    resolution: {integrity: sha512-CKW8Vu+uUZneQCPtXmSBUC6NCAUdya26hWCElAWh5mVSlSRsmiCPUUDKb3Z0szng1hiAJa098Hkhg9o4SE35Qw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.25.9':
    resolution: {integrity: sha512-TlprrJ1GBZ3r6s96Yq8gEQv82s8/5HnCVHtEJScUj90thHQbwe+E5MLhi2bbNHBEJuzrvltXSru+BUxHDoog7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.25.9':
    resolution: {integrity: sha512-fSaXafEE9CVHPweLYw4J0emp1t8zYTXyzN3UuG+lylqkvYd7RMrsOQ8TYx5RF231be0vqtFC6jnx3UmpJmKBYg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.25.9':
    resolution: {integrity: sha512-Kj/Gh+Rw2RNLbCK1VAWj2U48yxxqL2x0k10nPtSdRa0O2xnHXalD0s+o1A6a0W43gJ00ANo38jxkQreckOzv5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.25.9':
    resolution: {integrity: sha512-qM/6m6hQZzDcZF3onzIhZeDHDO43bkNNlOX0i8n3lR6zLbu0GN2d8qfM/IERJZYauhAHSLHy39NF0Ctdvcid7g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.25.9':
    resolution: {integrity: sha512-6AvV0FsLULbpnXeBjrY4dmWF8F7gf8QnvTEoO/wX/5xm/xE1Xo8oPuD3MPS+KS9f9XBEAWN7X1aWr4z9HdOr7A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.25.9':
    resolution: {integrity: sha512-wzz6MKwpnshBAiRmn4jR8LYz/g8Ksg0o80XmwZDlordjwEk9SxBzTWC7F5ef1jhbrbOW2DJ5J6ayRukrJmnr0g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.25.9':
    resolution: {integrity: sha512-D/JUozNpQLAPUVusvqMxyvjzllRaF8/nSrP1s2YGQT/W4LHK4xxsMcHjhOGTS01mp9Hda8nswb+FblLdJornQw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.25.9':
    resolution: {integrity: sha512-Evf3kcMqzXA3xfYJmZ9Pg1OvKdtqsDMSWBDzZOPLvHiTt36E75jLDQo5w1gtRU95Q4E5PDttrTf25Fw8d/uWLw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.25.9':
    resolution: {integrity: sha512-IvIUeV5KrS/VPavfSM/Iu+RE6llrHrYIKY1yfCzyO/lMXHQ+p7uGhonmGVisv6tSBSVgWzMBohTcvkC9vQcQFA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-constant-elements@7.25.9':
    resolution: {integrity: sha512-Ncw2JFsJVuvfRsa2lSHiC55kETQVLSnsYGQ1JDDwkUeWGTL/8Tom8aLTnlqgoeuopWrbbGndrc9AlLYrIosrow==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.25.9':
    resolution: {integrity: sha512-KJfMlYIUxQB1CJfO3e0+h0ZHWOTLCPP115Awhaz8U0Zpq36Gl/cXlpoyMRnUWlhNUBAzldnCiAZNvCDj7CrKxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-development@7.25.9':
    resolution: {integrity: sha512-9mj6rm7XVYs4mdLIpbZnHOYdpW42uoiBCTVowg7sP1thUOiANgMb4UtpRivR0pp5iL+ocvUv7X4mZgFRpJEzGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.25.9':
    resolution: {integrity: sha512-s5XwpQYCqGerXl+Pu6VDL3x0j2d82eiV77UJ8a2mDHAW7j9SWRqQ2y1fNo1Z74CdcYipl5Z41zvjj4Nfzq36rw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-pure-annotations@7.25.9':
    resolution: {integrity: sha512-KQ/Takk3T8Qzj5TppkS1be588lkbTp5uj7w6a0LeQaTMSckU/wK0oJ/pih+T690tkgI5jfmg2TqDJvd41Sj1Cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.27.0':
    resolution: {integrity: sha512-LX/vCajUJQDqE7Aum/ELUMZAY19+cDpghxrnyt5I1tV6X5PyC86AOoWXWFYFeIvauyeSA6/ktn4tQVn/3ZifsA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.26.0':
    resolution: {integrity: sha512-vN6saax7lrA2yA/Pak3sCxuD6F5InBjn9IcrIKQPjpsLvuHYLVroTxjdlVRHjjBWxKOqIwpTXDkOssYT4BFdRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.25.9':
    resolution: {integrity: sha512-7DL7DKYjn5Su++4RXu8puKZm2XBPHyjWLUidaPEkCUBbE7IPcsrkRHggAOOKydH1dASWdcUBxrkOGNxUv5P3Jg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.26.10':
    resolution: {integrity: sha512-NWaL2qG6HRpONTnj4JvDU6th4jYeZOJgu3QhmFTCihib0ermtOJqktA5BduGm3suhhVe9EMP9c9+mfJ/I9slqw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.25.9':
    resolution: {integrity: sha512-MUv6t0FhO5qHnS/W8XCbHmiRWOphNufpE1IVxhK5kuN3Td9FT1x4rx4K42s3RYdMXCXpfWkGSbCSd0Z64xA7Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.25.9':
    resolution: {integrity: sha512-oNknIB0TbURU5pqJFVbOOFspVlrpVwo2H1+HUIsVDvp5VauGGDP1ZEvO8Nn5xyMEs3dakajOxlmkNW7kNgSm6A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.25.9':
    resolution: {integrity: sha512-WqBUSgeVwucYDP9U/xNRQam7xV8W5Zf+6Eo7T2SRVUFlhRiMNFdFz58u0KZmCVVqs2i7SHgpRnAhzRNmKfi2uA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.26.8':
    resolution: {integrity: sha512-OmGDL5/J0CJPJZTHZbi2XpO0tyT2Ia7fzpW5GURwdtp2X3fMmN8au/ej6peC/T33/+CRiIpA8Krse8hFGVmT5Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.27.0':
    resolution: {integrity: sha512-+LLkxA9rKJpNoGsbLnAgOCdESl73vwYn+V6b+5wHbrE7OGKVDPHIQvbFSzqE6rwqaCw2RE+zdJrlLkcf8YOA0w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.27.0':
    resolution: {integrity: sha512-fRGGjO2UEGPjvEcyAZXRXAS8AfdaQoq7HnxAbJoAoW10B9xOKesmmndJv+Sym2a+9FHWZ9KbyyLCe9s0Sn5jtg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.25.9':
    resolution: {integrity: sha512-s5EDrE6bW97LtxOcGj1Khcx5AaXwiMmi4toFWRDP9/y0Woo6pXC+iyPu/KuhKtfSrNFd7jJB+/fkOtZy6aIC6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.25.9':
    resolution: {integrity: sha512-Jt2d8Ga+QwRluxRQ307Vlxa6dMrYEMZCgGxoPR8V52rxPyldHu3hdlHspxaqYmE7oID5+kB+UKUB/eWS+DkkWg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.25.9':
    resolution: {integrity: sha512-yoxstj7Rg9dlNn9UQxzk4fcNivwv4nUYz7fYXBaKxvw/lnmPuOm/ikoELygbYq68Bls3D/D+NBPHiLwZdZZ4HA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.25.9':
    resolution: {integrity: sha512-8BYqO3GeVNHtx69fdPshN3fnzUNLrWdHhk/icSwigksJGczKSizZ+Z6SBCxTs723Fr5VSNorTIK7a+R2tISvwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.26.9':
    resolution: {integrity: sha512-vX3qPGE8sEKEAZCWk05k3cpTAE3/nOYca++JA+Rd0z2NCNzabmYvEiSShKzm10zdquOIAVXsy2Ei/DTW34KlKQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/preset-react@7.26.3':
    resolution: {integrity: sha512-Nl03d6T9ky516DGK2YMxrTqvnpUW63TnJMOMonj+Zae0JiPC5BC9xPMSL6L8fiSpA5vP88qfygavVQvnLp+6Cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.27.0':
    resolution: {integrity: sha512-vxaPFfJtHhgeOVXRKuHpHPAOgymmy8V8I65T1q53R7GCZlefKeCaTyDs3zOPHTTbmquvNlQYC5klEvWsBAtrBQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.23.0':
    resolution: {integrity: sha512-RoPqO1qxWZPZkBKhNaoswZyfvAVr8NtikWfTS4h3ovzbqMghQtGvzDSUXCJEeOu7wz6yNr8ZTIDLkVOAT1z1kA==}
    engines: {node: '>=6.9.0'}

  '@babel/runtime@7.27.0':
    resolution: {integrity: sha512-VtPOkrdPHZsKc/clNqyi9WUA8TINkZ4cGk63UUE3u4pmB2k+ZMQRDuIOagv8UVd6j7k0T3+RRIb7beKTebNbcw==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.0':
    resolution: {integrity: sha512-2ncevenBqXI6qRMukPlXwHKHchC7RyMuu4xv5JBXRfOGVcTy1mXCD12qrp7Jsoxll1EV3+9sE4GugBVRjT2jFA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.0':
    resolution: {integrity: sha512-19lYZFzYVQkkHkl4Cy4WrAVcqBkgvV2YM2TU3xG6DIwO7O3ecbDPfW3yM3bjAGcqcQHi+CCtjMR3dIEHxsd6bA==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.23.0':
    resolution: {integrity: sha512-0oIyUfKoI3mSqMvsxBdclDwxXKXAUA8v/apZbc+iSyARYou1o8ZGDxbUYyLFoW2arqS2jDGqJuZvv1d/io1axg==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.0':
    resolution: {integrity: sha512-H45s8fVLYjbhFH62dIJ3WtmJ6RSPt/3DRO0ZcT2SUiYiQyz3BLVb9ADEnLl91m74aQPS3AzzeajZHYOalWe3bg==}
    engines: {node: '>=6.9.0'}

  '@chenshuai2144/sketch-color@1.0.9':
    resolution: {integrity: sha512-obzSy26cb7Pm7OprWyVpgMpIlrZpZ0B7vbrU0RMbvRg0YAI890S5Xy02Aj1Nhl4+KTbi1lVYHt6HQP8Hm9s+1w==}
    peerDependencies:
      react: '>=16.12.0'

  '@clouddesign/design-designmaterial@1.7.8':
    resolution: {integrity: sha1-OiokZw40CGk7FCdDaYdtmtFCu/Q=, tarball: http://registry.cnpm.3weijia.com/@clouddesign/design-designmaterial/download/@clouddesign/design-designmaterial-1.7.8.tgz}
    peerDependencies:
      '@svg/request': ^0.2.1
      axios: 1.5.0

  '@clouddesign/design-framework@1.0.1':
    resolution: {integrity: sha1-LmvKYxkwb8dnnUd9Y6SGx/IDgKA=, tarball: http://registry.cnpm.3weijia.com/@clouddesign/design-framework/download/@clouddesign/design-framework-1.0.1.tgz}

  '@clouddesign/multi_unit@1.0.1':
    resolution: {integrity: sha1-M8mcsxGs3AE0GHZxlV9S96fGIkQ=, tarball: http://registry.cnpm.3weijia.com/@clouddesign/multi_unit/download/@clouddesign/multi_unit-1.0.1.tgz}

  '@clouddesign/royscene_threejs_extend@0.0.142':
    resolution: {integrity: sha1-IG8BusFQp2dFnbgheRlxJ3DMNbE=, tarball: http://registry.cnpm.3weijia.com/@clouddesign/royscene_threejs_extend/download/@clouddesign/royscene_threejs_extend-0.0.142.tgz}
    peerDependencies:
      '@clouddesign/design-designmaterial': ^1.7.8
      '@clouddesign/design-framework': ^1.0.1
      '@sd/royprotocal': 3.0.4-0
      '@svg/oss-upload': ^2.0.3
      '@svg/request': ^0.2.1
      axios: ^1.6.8
      base64-js: ^1.5.1
      buffer: ^6.0.3
      fflate: ^0.8.2
      js-cookie: ^3.0.5
      spark-md5: ^3.0.2
      three: ^0.170.0

  '@csstools/normalize.css@12.1.1':
    resolution: {integrity: sha512-YAYeJ+Xqh7fUou1d1j9XHl44BmsuThiTr4iNrgCQ3J27IbhXsxXDGZ1cXv8Qvs99d4rBbLiSKy3+WZiet32PcQ==}

  '@csstools/postcss-cascade-layers@1.1.1':
    resolution: {integrity: sha512-+KdYrpKC5TgomQr2DlZF4lDEpHcoxnj5IGddYYfBWJAKfj1JtuHUIqMa+E1pJJ+z3kvDViWMqyqPlG4Ja7amQA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-color-function@1.1.1':
    resolution: {integrity: sha512-Bc0f62WmHdtRDjf5f3e2STwRAl89N2CLb+9iAwzrv4L2hncrbDwnQD9PCq0gtAt7pOI2leIV08HIBUd4jxD8cw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-font-format-keywords@1.0.1':
    resolution: {integrity: sha512-ZgrlzuUAjXIOc2JueK0X5sZDjCtgimVp/O5CEqTcs5ShWBa6smhWYbS0x5cVc/+rycTDbjjzoP0KTDnUneZGOg==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-hwb-function@1.0.2':
    resolution: {integrity: sha512-YHdEru4o3Rsbjmu6vHy4UKOXZD+Rn2zmkAmLRfPet6+Jz4Ojw8cbWxe1n42VaXQhD3CQUXXTooIy8OkVbUcL+w==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-ic-unit@1.0.1':
    resolution: {integrity: sha512-Ot1rcwRAaRHNKC9tAqoqNZhjdYBzKk1POgWfhN4uCOE47ebGcLRqXjKkApVDpjifL6u2/55ekkpnFcp+s/OZUw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-is-pseudo-class@2.0.7':
    resolution: {integrity: sha512-7JPeVVZHd+jxYdULl87lvjgvWldYu+Bc62s9vD/ED6/QTGjy0jy0US/f6BG53sVMTBJ1lzKZFpYmofBN9eaRiA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-nested-calc@1.0.0':
    resolution: {integrity: sha512-JCsQsw1wjYwv1bJmgjKSoZNvf7R6+wuHDAbi5f/7MbFhl2d/+v+TvBTU4BJH3G1X1H87dHl0mh6TfYogbT/dJQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-normalize-display-values@1.0.1':
    resolution: {integrity: sha512-jcOanIbv55OFKQ3sYeFD/T0Ti7AMXc9nM1hZWu8m/2722gOTxFg7xYu4RDLJLeZmPUVQlGzo4jhzvTUq3x4ZUw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-oklab-function@1.1.1':
    resolution: {integrity: sha512-nJpJgsdA3dA9y5pgyb/UfEzE7W5Ka7u0CX0/HIMVBNWzWemdcTH3XwANECU6anWv/ao4vVNLTMxhiPNZsTK6iA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-progressive-custom-properties@1.3.0':
    resolution: {integrity: sha512-ASA9W1aIy5ygskZYuWams4BzafD12ULvSypmaLJT2jvQ8G0M3I8PRQhC0h7mG0Z3LI05+agZjqSR9+K9yaQQjA==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.3

  '@csstools/postcss-stepped-value-functions@1.0.1':
    resolution: {integrity: sha512-dz0LNoo3ijpTOQqEJLY8nyaapl6umbmDcgj4AD0lgVQ572b2eqA1iGZYTTWhrcrHztWDDRAX2DGYyw2VBjvCvQ==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-text-decoration-shorthand@1.0.0':
    resolution: {integrity: sha512-c1XwKJ2eMIWrzQenN0XbcfzckOLLJiczqy+YvfGmzoVXd7pT9FfObiSEfzs84bpE/VqfpEuAZ9tCRbZkZxxbdw==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-trigonometric-functions@1.0.2':
    resolution: {integrity: sha512-woKaLO///4bb+zZC2s80l+7cm07M7268MsyG3M0ActXXEFi6SuhvriQYcb58iiKGbjwwIU7n45iRLEHypB47Og==}
    engines: {node: ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/postcss-unset-value@1.0.2':
    resolution: {integrity: sha512-c8J4roPBILnelAsdLr4XOAR/GsTm0GJi4XpcfvoWk3U6KiTCqiFYc63KhRMQQX35jYMp4Ao8Ij9+IZRgMfJp1g==}
    engines: {node: ^12 || ^14 || >=16}
    peerDependencies:
      postcss: ^8.2

  '@csstools/selector-specificity@2.2.0':
    resolution: {integrity: sha512-+OJ9konv95ClSTOJCmMZqpd5+YGsB2S+x6w3E1oaM8UuR5j8nTNHYSz8c9BEPGDOCMQYIEEGlVPj/VY64iTbGw==}
    engines: {node: ^14 || ^16 || >=18}
    peerDependencies:
      postcss-selector-parser: ^6.0.10

  '@ctrl/tinycolor@3.6.1':
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}

  '@discoveryjs/json-ext@0.5.7':
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}

  '@dnd-kit/accessibility@3.1.1':
    resolution: {integrity: sha512-2P+YgaXF+gRsIihwwY1gCsQSYnu9Zyj2py8kY5fFvUM1qm2WA2u639R6YNVfU4GWr+ZM5mqEsfHZZLoRONbemw==}
    peerDependencies:
      react: '>=16.8.0'

  '@dnd-kit/core@6.3.1':
    resolution: {integrity: sha512-xkGBRQQab4RLwgXxoqETICr6S5JlogafbhNsidmrkVv2YRs5MLwpjoF2qpiGjQt8S9AoxtIV603s0GIUpY5eYQ==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@dnd-kit/modifiers@6.0.1':
    resolution: {integrity: sha512-rbxcsg3HhzlcMHVHWDuh9LCjpOVAgqbV78wLGI8tziXY3+qcMQ61qVXIvNKQFuhj75dSfD+o+PYZQ/NUk2A23A==}
    peerDependencies:
      '@dnd-kit/core': ^6.0.6
      react: '>=16.8.0'

  '@dnd-kit/sortable@7.0.2':
    resolution: {integrity: sha512-wDkBHHf9iCi1veM834Gbk1429bd4lHX4RpAwT0y2cHLf246GAvU2sVw/oxWNpPKQNQRQaeGXhAVgrOl1IT+iyA==}
    peerDependencies:
      '@dnd-kit/core': ^6.0.7
      react: '>=16.8.0'

  '@dnd-kit/utilities@3.2.2':
    resolution: {integrity: sha512-+MKAJEOfaBe5SmV6t34p80MMKhjvUz0vRrvVJbPT0WElzaOJ/1xs+D+KDv+tD/NE5ujfrChEcshd4fLn0wpiqg==}
    peerDependencies:
      react: '>=16.8.0'
    dependencies:
      react: 18.2.0
      tslib: 2.8.1

  '@dnd-kit/core@6.3.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@dnd-kit/accessibility': 3.1.1(react@18.2.0)
      '@dnd-kit/utilities': 3.2.2(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tslib: 2.8.1

  '@dnd-kit/modifiers@6.0.1(@dnd-kit/core@6.3.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@dnd-kit/core': 6.3.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@dnd-kit/utilities': 3.2.2(react@18.2.0)
      react: 18.2.0
      tslib: 2.8.1

  '@dnd-kit/sortable@7.0.2(@dnd-kit/core@6.3.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@dnd-kit/core': 6.3.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@dnd-kit/utilities': 3.2.2(react@18.2.0)
      react: 18.2.0
      tslib: 2.8.1

  '@dnd-kit/utilities@3.2.2(react@18.2.0)':
    dependencies:
      react: 18.2.0
      tslib: 2.8.1

  '@efox/emp-compile-swc@1.3.3':
    dependencies:
      '@swc/core': 1.11.21
      '@swc/css': 0.0.20
      regenerator-runtime: 0.13.11
    transitivePeerDependencies:
      - '@swc/helpers'

  '@efox/emp-tsconfig@1.1.4(typescript@5.3.3)':
    dependencies:
      typescript-plugin-css-modules: 2.8.0(typescript@5.3.3)
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@efox/emp@2.7.1(browserslist@4.0.0)(eslint@9.25.0(jiti@1.21.7))(type-fest@2.19.0)(vue-template-compiler@2.7.16)(webpack-cli@5.1.4)':
    dependencies:
      '@babel/core': 7.23.0
      '@babel/plugin-proposal-class-properties': 7.18.6(@babel/core@7.23.0)
      '@babel/plugin-proposal-decorators': 7.25.9(@babel/core@7.23.0)
      '@babel/plugin-proposal-numeric-separator': 7.18.6(@babel/core@7.23.0)
      '@babel/plugin-proposal-private-methods': 7.18.6(@babel/core@7.23.0)
      '@babel/plugin-proposal-private-property-in-object': 7.21.11(@babel/core@7.23.0)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.23.0)
      '@babel/plugin-transform-runtime': 7.26.10(@babel/core@7.23.0)
      '@babel/plugin-transform-typescript': 7.27.0(@babel/core@7.23.0)
      '@babel/preset-env': 7.26.9(@babel/core@7.23.0)
      '@babel/preset-react': 7.26.3(@babel/core@7.23.0)
      '@babel/preset-typescript': 7.27.0(@babel/core@7.23.0)
      '@babel/runtime': 7.23.0
      '@pmmmwh/react-refresh-webpack-plugin': 0.5.16(react-refresh@0.14.2)(type-fest@2.19.0)(webpack-dev-server@4.15.2)(webpack@5.99.6)
      '@svgr/webpack': 8.1.0(typescript@4.9.5)
      '@vue/compiler-sfc': 3.5.13
      address: 1.2.2
      axios: 0.27.2(debug@4.4.0)
      babel-loader: 8.4.1(@babel/core@7.23.0)(webpack@5.99.6)
      babel-plugin-import: 1.13.8
      chalk: 4.1.2
      commander: 8.3.0
      compression: 1.8.0
      copy-webpack-plugin: 11.0.0(webpack@5.99.6)
      core-js: 3.41.0
      cors: 2.8.5
      css-loader: 6.11.0(webpack@5.99.6)
      css-minimizer-webpack-plugin: 4.2.2(webpack@5.99.6)
      default-gateway: 6.0.3
      dotenv-webpack: 8.1.0(webpack@5.99.6)
      eslint-webpack-plugin: 3.2.0(eslint@9.25.0(jiti@1.21.7))(webpack@5.99.6)
      express: 4.21.2
      fast-glob: 3.3.3
      filesize: 8.0.7
      fork-ts-checker-webpack-plugin: 7.3.0(typescript@4.9.5)(vue-template-compiler@2.7.16)(webpack@5.99.6)
      fs-extra: 10.1.0
      git-promise: 1.0.0
      gzip-size: 6.0.0
      html-webpack-plugin: 5.6.3(webpack@5.99.6)
      inquirer: 8.2.6
      less: 4.3.0
      less-loader: 10.2.0(less@4.3.0)(webpack@5.99.6)
      lodash: 4.17.21
      mini-css-extract-plugin: 2.9.2(webpack@5.99.6)
      nanospinner: 0.6.0
      postcss: 8.5.3
      postcss-flexbugs-fixes: 5.0.2(postcss@8.5.3)
      postcss-loader: 7.3.4(postcss@8.5.3)(typescript@4.9.5)(webpack@5.99.6)
      postcss-normalize: 10.0.1(browserslist@4.0.0)(postcss@8.5.3)
      postcss-preset-env: 7.8.3(postcss@8.5.3)
      postcss-pxtorem: 6.1.0(postcss@8.5.3)
      postcss-rem: 2.0.4
      react-refresh: 0.14.2
      recursive-readdir: 2.2.3
      regenerator-runtime: 0.13.11
      sass: 1.86.3
      sass-loader: 12.6.0(sass@1.86.3)(webpack@5.99.6)
      strip-ansi: 6.0.0
      style-loader: 3.3.4(webpack@5.99.6)
      terser-webpack-plugin: 5.3.14(webpack@5.99.6)
      typescript: 4.9.5
      typescript-plugin-css-modules: 3.4.0(typescript@4.9.5)
      url-loader: 4.1.1(webpack@5.99.6)
      vue-tsc: 1.8.27(typescript@4.9.5)
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-bundle-analyzer: 4.10.2
      webpack-chain: 6.5.1
      webpack-dev-server: 4.15.2(webpack-cli@5.1.4)(webpack@5.99.6)
      webpack-federated-stats-plugin: 2.0.9(webpack@5.99.6)
      webpack-manifest-plugin: 5.0.1(webpack@5.99.6)
      webpackbar: 5.0.2(webpack@5.99.6)
      worker-loader: 3.0.8(webpack@5.99.6)
    transitivePeerDependencies:
      - '@parcel/css'
      - '@rspack/core'
      - '@swc/core'
      - '@swc/css'
      - '@types/webpack'
      - browserslist
      - bufferutil
      - clean-css
      - csso
      - debug
      - esbuild
      - eslint
      - fibers
      - file-loader
      - lightningcss
      - node-sass
      - sass-embedded
      - sockjs-client
      - supports-color
      - ts-node
      - type-fest
      - uglify-js
      - utf-8-validate
      - vue-template-compiler
      - webpack-cli
      - webpack-hot-middleware
      - webpack-plugin-serve

  '@efox/eslint-config-react-prittier-ts@1.2.6(eslint-plugin-prettier@3.4.1(eslint@9.25.0(jiti@1.21.7))(prettier@2.8.8))(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/eslint-plugin': 4.33.0(@typescript-eslint/parser@4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3))(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/parser': 4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      eslint: 9.25.0(jiti@1.21.7)
      eslint-config-prettier: 6.15.0(eslint@9.25.0(jiti@1.21.7))
      eslint-plugin-prettier: 3.4.1(eslint@9.25.0(jiti@1.21.7))(prettier@2.8.8)
      eslint-plugin-react: 7.37.5(eslint@9.25.0(jiti@1.21.7))
      eslint-plugin-react-hooks: 4.6.2(eslint@9.25.0(jiti@1.21.7))
      prettier: 2.8.8
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@emotion/babel-plugin@11.13.5':
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/runtime': 7.23.0
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/serialize': 1.3.3
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/cache@11.14.0':
    dependencies:
      '@emotion/memoize': 0.9.0
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      stylis: 4.2.0

  '@emotion/css@11.13.5':
    dependencies:
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.2
    transitivePeerDependencies:
      - supports-color

  '@emotion/hash@0.8.0': {}

  '@emotion/hash@0.9.2': {}

  '@emotion/memoize@0.9.0': {}

  '@emotion/react@11.14.0(@types/react@18.2.55)(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.23.0
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@18.2.0)
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      hoist-non-react-statics: 3.3.2
      react: 18.2.0
    optionalDependencies:
      '@types/react': 18.2.55
    transitivePeerDependencies:
      - supports-color

  '@emotion/serialize@1.3.3':
    dependencies:
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/unitless': 0.10.0
      '@emotion/utils': 1.4.2
      csstype: 3.1.3

  '@emotion/server@11.11.0(@emotion/css@11.13.5)':
    dependencies:
      '@emotion/utils': 1.4.2
      html-tokenize: 2.0.1
      multipipe: 1.0.2
      through: 2.3.8
    optionalDependencies:
      '@emotion/css': 11.13.5

  '@emotion/sheet@1.4.0': {}

  '@emotion/unitless@0.10.0': {}

  '@emotion/unitless@0.7.5': {}

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.2.0)':
    dependencies:
      react: 18.2.0

  '@emotion/utils@1.4.2': {}

  '@emotion/weak-memoize@0.4.0': {}

  '@eslint-community/eslint-utils@4.6.1(eslint@9.25.0(jiti@1.21.7))':
    dependencies:
      eslint: 9.25.0(jiti@1.21.7)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.20.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.2.1': {}

  '@eslint/core@0.13.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.25.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.2.8':
    dependencies:
      '@eslint/core': 0.13.0
      levn: 0.4.1

  '@formatjs/ecma402-abstract@2.3.4':
    dependencies:
      '@formatjs/fast-memoize': 2.2.7
      '@formatjs/intl-localematcher': 0.6.1
      decimal.js: 10.5.0
      tslib: 2.8.1

  '@formatjs/fast-memoize@2.2.7':
    dependencies:
      tslib: 2.8.1

  '@formatjs/icu-messageformat-parser@2.11.2':
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.4
      '@formatjs/icu-skeleton-parser': 1.8.14
      tslib: 2.8.1

  '@formatjs/icu-skeleton-parser@1.8.14':
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.4
      tslib: 2.8.1

  '@formatjs/intl-localematcher@0.6.1':
    dependencies:
      tslib: 2.8.1

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.2': {}

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/types@29.6.3':
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 20.17.30
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@layoutai/basic_data@1.0.2(@layoutai/z_polygon@0.0.2)':
    dependencies:
      '@layoutai/z_polygon': 0.0.2

  '@layoutai/model3d_api@1.0.7(@layoutai/z_polygon@0.0.2)(tough-cookie@5.1.2)':
    dependencies:
      '@layoutai/basic_data': 1.0.2(@layoutai/z_polygon@0.0.2)
      '@svg/request': 0.4.0(axios@0.27.2)
      axios: 0.27.2(debug@4.4.0)
      axios-cookiejar-support: 5.0.5(axios@0.27.2)(tough-cookie@5.1.2)
      buffer: 6.0.3
      fflate: 0.8.2
    transitivePeerDependencies:
      - '@layoutai/z_polygon'
      - debug
      - tough-cookie
      - undici

  '@layoutai/z_polygon@0.0.2': {}

  '@leichtgewicht/ip-codec@2.0.5': {}

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1
    optional: true

  '@playwright/test@1.52.0':
    dependencies:
      playwright: 1.52.0

  '@pmmmwh/react-refresh-webpack-plugin@0.5.16(react-refresh@0.14.2)(type-fest@2.19.0)(webpack-dev-server@4.15.2)(webpack@5.99.6)':
    dependencies:
      ansi-html: 0.0.9
      core-js-pure: 3.41.0
      error-stack-parser: 2.1.4
      html-entities: 2.6.0
      loader-utils: 2.0.4
      react-refresh: 0.14.2
      schema-utils: 4.3.0
      source-map: 0.7.4
      webpack: 5.99.6(webpack-cli@5.1.4)
    optionalDependencies:
      type-fest: 2.19.0
      webpack-dev-server: 4.15.2(webpack-cli@5.1.4)(webpack@5.99.6)

  '@polka/url@1.0.0-next.29': {}

  '@rc-component/async-validator@5.0.4':
    dependencies:
      '@babel/runtime': 7.27.0

  '@rc-component/color-picker@2.0.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@ant-design/fast-color': 2.0.6
      '@babel/runtime': 7.27.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@rc-component/context@1.4.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.23.0
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@rc-component/mini-decimal@1.1.0':
    dependencies:
      '@babel/runtime': 7.23.0

  '@rc-component/mutate-observer@1.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@rc-component/portal@1.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@rc-component/qrcode@1.0.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.27.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@rc-component/tour@1.15.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@rc-component/trigger@2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@babel/runtime': 7.27.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  '@sd/plugin_svgsocketclient@3.0.12-0(@sd/roybase@3.0.38-0)(@sd/royframebase@3.0.20-0(@sd/roybase@3.0.38-0)(@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0))(@sd/royprotocal@3.0.4-0))(@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0))(@sd/roysvgapi@3.0.24-0)':
    dependencies:
      '@sd/roybase': 3.0.38-0
      '@sd/royframebase': 3.0.20-0(@sd/roybase@3.0.38-0)(@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0))(@sd/royprotocal@3.0.4-0)
      '@sd/royinterface': 3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0)
      '@sd/roysvgapi': 3.0.24-0

  '@sd/roybase@3.0.38-0': {}

  '@sd/roybase@3.0.56-0': {}

  '@sd/royframebase@3.0.20-0(@sd/roybase@3.0.38-0)(@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0))(@sd/royprotocal@3.0.4-0)':
    dependencies:
      '@sd/roybase': 3.0.38-0
      '@sd/royinterface': 3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0)
      '@sd/royprotocal': 3.0.4-0

  '@sd/royinterface@3.0.25-0(@sd/roybase@3.0.38-0)(@sd/royprotocal@3.0.4-0)':
    dependencies:
      '@sd/roybase': 3.0.38-0
      '@sd/royprotocal': 3.0.4-0

  '@sd/royprotocal@3.0.4-0':
    dependencies:
      '@sd/roybase': 3.0.56-0

  '@sd/roysvgapi@3.0.24-0': {}

  '@sinclair/typebox@0.27.8': {}

  '@stagewise-plugins/react@0.4.7(@stagewise/toolbar@0.4.6)':
    dependencies:
      '@stagewise/toolbar': 0.4.6

  '@stagewise/toolbar-react@0.4.6(@types/react@18.2.55)(react@18.2.0)':
    dependencies:
      '@stagewise/toolbar': 0.4.6
      '@types/react': 18.2.55
      react: 18.2.0

  '@stagewise/toolbar@0.4.6': {}

  '@svg/antd-basic@5.6.8(@svg/antd@2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(@types/express@4.17.21)(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(rc-field-form@2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@ant-design/icons': 4.8.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@ant-design/pro-components': 2.8.7(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(rc-field-form@2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/antd': 2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/lang': 2.9.2
      '@svg/oss-upload': 2.2.0(@types/express@4.17.21)
      antd-style: 3.7.1(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      axios: 0.27.2(debug@4.4.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      uuid: 9.0.1
    transitivePeerDependencies:
      - '@types/express'
      - '@types/react'
      - antd
      - debug
      - proxy-agent
      - rc-field-form
      - supports-color

  '@svg/antd-cloud-design@4.17.0(@svg/antd@2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(@svg/request@0.4.0(axios@0.27.2))(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@ant-design/icons': 4.8.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@clouddesign/multi_unit': 1.0.1
      '@svg/antd': 2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/lang': 2.9.2
      '@svg/request': 0.4.0(axios@0.27.2)
      '@types/pako': 2.0.3
      antd-style: 3.7.1(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      form-render: 2.2.5(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      pako: 2.1.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-draggable: 4.4.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - antd
      - immer
      - supports-color

  '@svg/antd@2.6.3(@svg/lang@2.9.2)(@types/react@18.2.55)(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    dependencies:
      '@ant-design/icons': 5.6.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@svg/lang': 2.9.2
      antd: 5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      antd-style: 3.6.3(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - date-fns
      - luxon
      - moment
      - supports-color

  '@svg/deploy@2.1.0':
    dependencies:
      axios: 1.11.0
    transitivePeerDependencies:
      - debug

  '@svg/lang@2.9.2': {}

  '@svg/oss-upload@2.2.0(@types/express@4.17.21)':
    dependencies:
      '@volcengine/tos-sdk': 2.7.4(@types/express@4.17.21)
      ali-oss: 6.17.1
      cos-js-sdk-v5: 1.8.7
      esdk-obs-browserjs: 3.24.3
      webuploader: 0.1.8
    transitivePeerDependencies:
      - '@types/express'
      - debug
      - proxy-agent
      - supports-color

  '@svg/request@0.4.0(axios@0.27.2)':
    dependencies:
      axios: 0.27.2(debug@4.4.0)

  '@svg/sensors@2.5.2':
    dependencies:
      core-js: 2.6.12
      sa-sdk-javascript: 1.16.3

  '@svg/sso-plus@1.1.0':
    dependencies:
      whatwg-fetch: 3.6.20

  '@svgr/babel-plugin-add-jsx-attribute@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-svg-dynamic-title@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-svg-em-dimensions@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-transform-react-native-svg@8.1.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-plugin-transform-svg-component@8.0.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0

  '@svgr/babel-preset@8.1.0(@babel/core@7.23.0)':
    dependencies:
      '@babel/core': 7.23.0
      '@svgr/babel-plugin-add-jsx-attribute': 8.0.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-remove-jsx-attribute': 8.0.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-remove-jsx-empty-expression': 8.0.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-replace-jsx-attribute-value': 8.0.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-svg-dynamic-title': 8.0.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-svg-em-dimensions': 8.0.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-transform-react-native-svg': 8.1.0(@babel/core@7.23.0)
      '@svgr/babel-plugin-transform-svg-component': 8.0.0(@babel/core@7.23.0)

  '@svgr/core@8.1.0(typescript@4.9.5)':
    dependencies:
      '@babel/core': 7.23.0
      '@svgr/babel-preset': 8.1.0(@babel/core@7.23.0)
      camelcase: 6.3.0
      cosmiconfig: 8.3.6(typescript@4.9.5)
      snake-case: 3.0.4
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@svgr/hast-util-to-babel-ast@8.0.0':
    dependencies:
      '@babel/types': 7.23.0
      entities: 4.5.0

  '@svgr/plugin-jsx@8.1.0(@svgr/core@8.1.0(typescript@4.9.5))':
    dependencies:
      '@babel/core': 7.23.0
      '@svgr/babel-preset': 8.1.0(@babel/core@7.23.0)
      '@svgr/core': 8.1.0(typescript@4.9.5)
      '@svgr/hast-util-to-babel-ast': 8.0.0
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color

  '@svgr/plugin-svgo@8.1.0(@svgr/core@8.1.0(typescript@4.9.5))(typescript@4.9.5)':
    dependencies:
      '@svgr/core': 8.1.0(typescript@4.9.5)
      cosmiconfig: 8.3.6(typescript@4.9.5)
      deepmerge: 4.3.1
      svgo: 3.3.2
    transitivePeerDependencies:
      - typescript

  '@svgr/webpack@8.1.0(typescript@4.9.5)':
    dependencies:
      '@babel/core': 7.23.0
      '@babel/plugin-transform-react-constant-elements': 7.25.9(@babel/core@7.23.0)
      '@babel/preset-env': 7.26.9(@babel/core@7.23.0)
      '@babel/preset-react': 7.26.3(@babel/core@7.23.0)
      '@babel/preset-typescript': 7.27.0(@babel/core@7.23.0)
      '@svgr/core': 8.1.0(typescript@4.9.5)
      '@svgr/plugin-jsx': 8.1.0(@svgr/core@8.1.0(typescript@4.9.5))
      '@svgr/plugin-svgo': 8.1.0(@svgr/core@8.1.0(typescript@4.9.5))(typescript@4.9.5)
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@swc/core-darwin-arm64@1.11.21':
    optional: true

  '@swc/core-darwin-x64@1.11.21':
    optional: true

  '@swc/core-linux-arm-gnueabihf@1.11.21':
    optional: true

  '@swc/core-linux-arm64-gnu@1.11.21':
    optional: true

  '@swc/core-linux-arm64-musl@1.11.21':
    optional: true

  '@swc/core-linux-x64-gnu@1.11.21':
    optional: true

  '@swc/core-linux-x64-musl@1.11.21':
    optional: true

  '@swc/core-win32-arm64-msvc@1.11.21':
    optional: true

  '@swc/core-win32-ia32-msvc@1.11.21':
    optional: true

  '@swc/core-win32-x64-msvc@1.11.21':
    optional: true

  '@swc/core@1.11.21':
    dependencies:
      '@swc/counter': 0.1.3
      '@swc/types': 0.1.21
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.11.21
      '@swc/core-darwin-x64': 1.11.21
      '@swc/core-linux-arm-gnueabihf': 1.11.21
      '@swc/core-linux-arm64-gnu': 1.11.21
      '@swc/core-linux-arm64-musl': 1.11.21
      '@swc/core-linux-x64-gnu': 1.11.21
      '@swc/core-linux-x64-musl': 1.11.21
      '@swc/core-win32-arm64-msvc': 1.11.21
      '@swc/core-win32-ia32-msvc': 1.11.21
      '@swc/core-win32-x64-msvc': 1.11.21

  '@swc/counter@0.1.3': {}

  '@swc/css-android-arm-eabi@0.0.20':
    optional: true

  '@swc/css-android-arm64@0.0.20':
    optional: true

  '@swc/css-darwin-arm64@0.0.20':
    optional: true

  '@swc/css-darwin-x64@0.0.20':
    optional: true

  '@swc/css-freebsd-x64@0.0.20':
    optional: true

  '@swc/css-linux-arm-gnueabihf@0.0.20':
    optional: true

  '@swc/css-linux-arm64-gnu@0.0.20':
    optional: true

  '@swc/css-linux-arm64-musl@0.0.20':
    optional: true

  '@swc/css-linux-x64-gnu@0.0.20':
    optional: true

  '@swc/css-linux-x64-musl@0.0.20':
    optional: true

  '@swc/css-win32-arm64-msvc@0.0.20':
    optional: true

  '@swc/css-win32-ia32-msvc@0.0.20':
    optional: true

  '@swc/css-win32-x64-msvc@0.0.20':
    optional: true

  '@swc/css@0.0.20':
    optionalDependencies:
      '@swc/css-android-arm-eabi': 0.0.20
      '@swc/css-android-arm64': 0.0.20
      '@swc/css-darwin-arm64': 0.0.20
      '@swc/css-darwin-x64': 0.0.20
      '@swc/css-freebsd-x64': 0.0.20
      '@swc/css-linux-arm-gnueabihf': 0.0.20
      '@swc/css-linux-arm64-gnu': 0.0.20
      '@swc/css-linux-arm64-musl': 0.0.20
      '@swc/css-linux-x64-gnu': 0.0.20
      '@swc/css-linux-x64-musl': 0.0.20
      '@swc/css-win32-arm64-msvc': 0.0.20
      '@swc/css-win32-ia32-msvc': 0.0.20
      '@swc/css-win32-x64-msvc': 0.0.20

  '@swc/types@0.1.21':
    dependencies:
      '@swc/counter': 0.1.3

  '@trysound/sax@0.2.0': {}

  '@tweenjs/tween.js@23.1.3': {}

  '@types/body-parser@1.19.5':
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 20.17.30

  '@types/bonjour@3.5.13':
    dependencies:
      '@types/node': 20.17.30

  '@types/connect-history-api-fallback@1.5.4':
    dependencies:
      '@types/express-serve-static-core': 5.0.6
      '@types/node': 20.17.30

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 20.17.30

  '@types/css-modules@1.0.5': {}

  '@types/d3-array@3.2.1': {}

  '@types/d3-color@3.1.3': {}

  '@types/d3-ease@3.0.2': {}

  '@types/d3-interpolate@3.0.4':
    dependencies:
      '@types/d3-color': 3.1.3

  '@types/d3-path@3.1.1': {}

  '@types/d3-scale@4.0.9':
    dependencies:
      '@types/d3-time': 3.0.4

  '@types/d3-shape@3.1.7':
    dependencies:
      '@types/d3-path': 3.1.1

  '@types/d3-time@3.0.4': {}

  '@types/d3-timer@3.0.2': {}

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.7

  '@types/eslint@8.56.12':
    dependencies:
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.7': {}

  '@types/express-serve-static-core@4.19.6':
    dependencies:
      '@types/node': 20.17.30
      '@types/qs': 6.9.18
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4

  '@types/express-serve-static-core@5.0.6':
    dependencies:
      '@types/node': 20.17.30
      '@types/qs': 6.9.18
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4

  '@types/express@4.17.21':
    dependencies:
      '@types/body-parser': 1.19.5
      '@types/express-serve-static-core': 4.19.6
      '@types/qs': 6.9.18
      '@types/serve-static': 1.15.7

  '@types/history@4.7.11': {}

  '@types/html-minifier-terser@6.1.0': {}

  '@types/http-errors@2.0.4': {}

  '@types/http-proxy@1.17.16':
    dependencies:
      '@types/node': 20.17.30

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/js-cookie@2.2.7': {}

  '@types/js-cookie@3.0.6': {}

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/lodash@4.17.17': {}

  '@types/mime@1.3.5': {}

  '@types/node-forge@1.3.11':
    dependencies:
      '@types/node': 20.17.30

  '@types/node@18.19.86':
    dependencies:
      undici-types: 5.26.5

  '@types/node@20.17.30':
    dependencies:
      undici-types: 6.19.8

  '@types/pako@2.0.3': {}

  '@types/parse-json@4.0.2': {}

  '@types/prop-types@15.7.14': {}

  '@types/qs@6.9.18': {}

  '@types/range-parser@1.2.7': {}

  '@types/react-dom@18.2.19':
    dependencies:
      '@types/react': 18.2.55

  '@types/react-router-dom@5.3.3':
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 18.2.55
      '@types/react-router': 5.1.20

  '@types/react-router@5.1.20':
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 18.2.55

  /@types/react/18.2.55:
    resolution: {integrity: sha512-Y2Tz5P4yz23brwm2d7jNon39qoAtMMmalOQv6+fEFt1mT+FcM3D841wDpoUvFXhaYenuROCy3FZYqdTjM7qVyA==}
    dependencies:
      '@types/prop-types': 15.7.15
      '@types/scheduler': 0.26.0
      csstype: 3.1.3

  '@types/retry@0.12.0': {}

  '@types/scheduler@0.26.0': {}

  '@types/send@0.17.4':
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 20.17.30

  '@types/serve-index@1.9.4':
    dependencies:
      '@types/express': 4.17.21

  '@types/serve-static@1.15.7':
    dependencies:
      '@types/http-errors': 2.0.4
      '@types/node': 20.17.30
      '@types/send': 0.17.4

  '@types/sockjs@0.3.36':
    dependencies:
      '@types/node': 20.17.30

  '@types/spark-md5@3.0.5': {}

  '@types/stats.js@0.17.3': {}

  '@types/three@0.171.0':
    dependencies:
      '@tweenjs/tween.js': 23.1.3
      '@types/stats.js': 0.17.3
      '@types/webxr': 0.5.22
      '@webgpu/types': 0.1.60
      fflate: 0.8.2
      meshoptimizer: 0.18.1

  '@types/webxr@0.5.22': {}

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 20.17.30

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@typescript-eslint/eslint-plugin@4.33.0(@typescript-eslint/parser@4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3))(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/experimental-utils': 4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/parser': 4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/scope-manager': 4.33.0
      debug: 4.4.0
      eslint: 9.25.0(jiti@1.21.7)
      functional-red-black-tree: 1.0.1
      ignore: 5.3.2
      regexpp: 3.2.0
      semver: 7.7.1
      tsutils: 3.21.0(typescript@5.3.3)
    optionalDependencies:
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/eslint-plugin@8.30.1(@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3))(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/scope-manager': 8.30.1
      '@typescript-eslint/type-utils': 8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/utils': 8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/visitor-keys': 8.30.1
      eslint: 9.25.0(jiti@1.21.7)
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.3.3)
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/experimental-utils@4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@types/json-schema': 7.0.15
      '@typescript-eslint/scope-manager': 4.33.0
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/typescript-estree': 4.33.0(typescript@5.3.3)
      eslint: 9.25.0(jiti@1.21.7)
      eslint-scope: 5.1.1
      eslint-utils: 3.0.0(eslint@9.25.0(jiti@1.21.7))
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/parser@4.33.0(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 4.33.0
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/typescript-estree': 4.33.0(typescript@5.3.3)
      debug: 4.4.0
      eslint: 9.25.0(jiti@1.21.7)
    optionalDependencies:
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.30.1
      '@typescript-eslint/types': 8.30.1
      '@typescript-eslint/typescript-estree': 8.30.1(typescript@5.3.3)
      '@typescript-eslint/visitor-keys': 8.30.1
      debug: 4.4.0
      eslint: 9.25.0(jiti@1.21.7)
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@4.33.0':
    dependencies:
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/visitor-keys': 4.33.0

  '@typescript-eslint/scope-manager@8.30.1':
    dependencies:
      '@typescript-eslint/types': 8.30.1
      '@typescript-eslint/visitor-keys': 8.30.1

  '@typescript-eslint/type-utils@8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.30.1(typescript@5.3.3)
      '@typescript-eslint/utils': 8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      debug: 4.4.0
      eslint: 9.25.0(jiti@1.21.7)
      ts-api-utils: 2.1.0(typescript@5.3.3)
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@4.33.0': {}

  '@typescript-eslint/types@8.30.1': {}

  '@typescript-eslint/typescript-estree@4.33.0(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/types': 4.33.0
      '@typescript-eslint/visitor-keys': 4.33.0
      debug: 4.4.0
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.7.1
      tsutils: 3.21.0(typescript@5.3.3)
    optionalDependencies:
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/typescript-estree@8.30.1(typescript@5.3.3)':
    dependencies:
      '@typescript-eslint/types': 8.30.1
      '@typescript-eslint/visitor-keys': 8.30.1
      debug: 4.4.0
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.1
      ts-api-utils: 2.1.0(typescript@5.3.3)
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.6.1(eslint@9.25.0(jiti@1.21.7))
      '@typescript-eslint/scope-manager': 8.30.1
      '@typescript-eslint/types': 8.30.1
      '@typescript-eslint/typescript-estree': 8.30.1(typescript@5.3.3)
      eslint: 9.25.0(jiti@1.21.7)
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@4.33.0':
    dependencies:
      '@typescript-eslint/types': 4.33.0
      eslint-visitor-keys: 2.1.0

  '@typescript-eslint/visitor-keys@8.30.1':
    dependencies:
      '@typescript-eslint/types': 8.30.1
      eslint-visitor-keys: 4.2.0

  '@umijs/route-utils@4.0.1': {}

  '@umijs/use-params@1.0.9(react@18.2.0)':
    dependencies:
      react: 18.2.0

  '@volar/language-core@1.11.1':
    dependencies:
      '@volar/source-map': 1.11.1

  '@volar/source-map@1.11.1':
    dependencies:
      muggle-string: 0.3.1

  '@volar/typescript@1.11.1':
    dependencies:
      '@volar/language-core': 1.11.1
      path-browserify: 1.0.1

  '@volcengine/tos-sdk@2.7.4(@types/express@4.17.21)':
    dependencies:
      axios: 0.21.4(debug@4.4.0)
      axios-adapter-uniapp: 0.1.4(debug@4.4.0)
      crypto-js: 4.2.0
      debug: 4.4.0
      http-proxy-middleware: 2.0.9(@types/express@4.17.21)(debug@4.4.0)
      lodash: 4.17.21
      qs: 6.14.0
      tos-crc64-js: 0.0.1
      type-fest: 1.4.0
    transitivePeerDependencies:
      - '@types/express'
      - supports-color

  '@vue/compiler-core@3.5.13':
    dependencies:
      '@babel/parser': 7.27.0
      '@vue/shared': 3.5.13
      entities: 4.5.0
      estree-walker: 2.0.2
      source-map-js: 1.2.1

  '@vue/compiler-dom@3.5.13':
    dependencies:
      '@vue/compiler-core': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/compiler-sfc@3.5.13':
    dependencies:
      '@babel/parser': 7.27.0
      '@vue/compiler-core': 3.5.13
      '@vue/compiler-dom': 3.5.13
      '@vue/compiler-ssr': 3.5.13
      '@vue/shared': 3.5.13
      estree-walker: 2.0.2
      magic-string: 0.30.17
      postcss: 8.5.3
      source-map-js: 1.2.1

  '@vue/compiler-ssr@3.5.13':
    dependencies:
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13

  '@vue/language-core@1.8.27(typescript@4.9.5)':
    dependencies:
      '@volar/language-core': 1.11.1
      '@volar/source-map': 1.11.1
      '@vue/compiler-dom': 3.5.13
      '@vue/shared': 3.5.13
      computeds: 0.0.1
      minimatch: 9.0.5
      muggle-string: 0.3.1
      path-browserify: 1.0.1
      vue-template-compiler: 2.7.16
    optionalDependencies:
      typescript: 4.9.5

  '@vue/shared@3.5.13': {}

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@webgpu/types@0.1.60': {}

  '@webpack-cli/configtest@2.1.1(webpack-cli@5.1.4)(webpack@5.99.6)':
    dependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-cli: 5.1.4(webpack-dev-server@4.15.2)(webpack@5.99.6)

  '@webpack-cli/info@2.0.2(webpack-cli@5.1.4)(webpack@5.99.6)':
    dependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-cli: 5.1.4(webpack-dev-server@4.15.2)(webpack@5.99.6)

  '@webpack-cli/serve@2.0.5(webpack-cli@5.1.4)(webpack-dev-server@4.15.2)(webpack@5.99.6)':
    dependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-cli: 5.1.4(webpack-dev-server@4.15.2)(webpack@5.99.6)
    optionalDependencies:
      webpack-dev-server: 4.15.2(webpack-cli@5.1.4)(webpack@5.99.6)

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-jsx@5.3.2(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.14.1

  acorn@8.14.1: {}

  add-dom-event-listener@1.1.0:
    dependencies:
      object-assign: 4.1.1

  address@1.2.2: {}

  agent-base@7.1.3: {}

  agentkeepalive@3.5.3:
    dependencies:
      humanize-ms: 1.2.1

  ahooks-v3-count@1.0.0: {}

  ahooks@3.7.5(react@18.2.0):
    dependencies:
      '@types/js-cookie': 2.2.7
      ahooks-v3-count: 1.0.0
      dayjs: 1.11.13
      intersection-observer: 0.12.2
      js-cookie: 2.2.1
      lodash: 4.17.21
      react: 18.2.0
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.8.1

  ahooks@3.8.4(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      dayjs: 1.11.13
      intersection-observer: 0.12.2
      js-cookie: 3.0.5
      lodash: 4.17.21
      react: 18.2.0
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.8.1

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ali-oss@6.17.1:
    dependencies:
      address: 1.2.2
      agentkeepalive: 3.5.3
      bowser: 1.9.4
      copy-to: 2.0.1
      dateformat: 2.2.0
      debug: 2.6.9
      destroy: 1.2.0
      end-or-error: 1.0.1
      get-ready: 1.0.0
      humanize-ms: 1.2.1
      is-type-of: 1.4.0
      js-base64: 2.6.4
      jstoxml: 2.2.9
      merge-descriptors: 1.0.3
      mime: 2.6.0
      mz-modules: 2.1.0
      platform: 1.3.6
      pump: 3.0.2
      sdk-base: 2.0.1
      stream-http: 2.8.2
      stream-wormhole: 1.1.0
      urllib: 2.44.0
      utility: 1.18.0
      xml2js: 0.4.23
    transitivePeerDependencies:
      - proxy-agent
      - supports-color

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-html-community@0.0.8: {}

  ansi-html@0.0.9: {}

  ansi-regex@5.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  antd-style@3.6.3(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@ant-design/cssinjs': 1.23.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@babel/runtime': 7.27.0
      '@emotion/cache': 11.14.0
      '@emotion/css': 11.13.5
      '@emotion/react': 11.14.0(@types/react@18.2.55)(react@18.2.0)
      '@emotion/serialize': 1.3.3
      '@emotion/server': 11.11.0(@emotion/css@11.13.5)
      '@emotion/utils': 1.4.2
      antd: 5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      use-merge-value: 1.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - react-dom
      - supports-color

  antd-style@3.7.1(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@ant-design/cssinjs': 1.23.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@babel/runtime': 7.27.0
      '@emotion/cache': 11.14.0
      '@emotion/css': 11.13.5
      '@emotion/react': 11.14.0(@types/react@18.2.55)(react@18.2.0)
      '@emotion/serialize': 1.3.3
      '@emotion/utils': 1.4.2
      antd: 5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      use-merge-value: 1.2.0(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - react-dom
      - supports-color

  antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@ant-design/colors': 7.2.0
      '@ant-design/cssinjs': 1.23.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@ant-design/cssinjs-utils': 1.1.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@ant-design/fast-color': 2.0.6
      '@ant-design/icons': 5.6.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@ant-design/react-slick': 1.1.2(react@18.2.0)
      '@babel/runtime': 7.27.0
      '@rc-component/color-picker': 2.0.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@rc-component/mutate-observer': 1.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@rc-component/qrcode': 1.0.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@rc-component/tour': 1.15.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      copy-to-clipboard: 3.3.3
      dayjs: 1.11.13
      rc-cascader: 3.33.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-checkbox: 3.5.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-collapse: 3.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-dialog: 9.6.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-drawer: 7.2.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-dropdown: 4.2.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-field-form: 2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-image: 7.11.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-input: 1.7.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-input-number: 9.4.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-mentions: 2.19.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-menu: 9.16.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-notification: 5.6.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-pagination: 5.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-picker: 4.11.3(date-fns@2.30.0)(dayjs@1.11.13)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-progress: 4.0.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-rate: 2.13.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-segmented: 2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-select: 14.16.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-slider: 11.1.8(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-steps: 6.0.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-switch: 4.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-table: 7.50.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-tabs: 15.5.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-textarea: 1.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-tooltip: 6.4.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-tree: 5.13.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-tree-select: 5.27.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-upload: 4.8.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      scroll-into-view-if-needed: 3.1.0
      throttle-debounce: 5.0.2
    transitivePeerDependencies:
      - date-fns
      - luxon
      - moment

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-flatten@1.1.1: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1

  array-union@2.1.0: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  asap@2.0.6: {}

  asn1.js@4.10.1:
    dependencies:
      bn.js: 4.12.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  assert@2.1.0:
    dependencies:
      call-bind: 1.0.8
      is-nan: 1.3.2
      object-is: 1.1.6
      object.assign: 4.1.7
      util: 0.12.5

  async-function@1.0.0: {}

  async-validator@3.5.2: {}

  asynckit@0.4.0: {}

  atob@2.1.2: {}

  autoprefixer@10.4.21(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-lite: 1.0.30001715
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axios-adapter-uniapp@0.1.4(debug@4.4.0):
    dependencies:
      axios: 0.27.2(debug@4.4.0)
    transitivePeerDependencies:
      - debug

  axios-cookiejar-support@5.0.5(axios@0.27.2)(tough-cookie@5.1.2):
    dependencies:
      axios: 0.27.2(debug@4.4.0)
      http-cookie-agent: 6.0.8(tough-cookie@5.1.2)
      tough-cookie: 5.1.2
    transitivePeerDependencies:
      - undici

  axios@0.21.4(debug@4.4.0):
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.0)
    transitivePeerDependencies:
      - debug

  axios@0.26.1:
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.0)
    transitivePeerDependencies:
      - debug

  axios@0.27.2(debug@4.4.0):
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.0)
      form-data: 4.0.4
    transitivePeerDependencies:
      - debug

  axios@1.11.0:
    dependencies:
      follow-redirects: 1.15.9(debug@4.4.0)
      form-data: 4.0.4
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-loader@8.4.1(@babel/core@7.23.0)(webpack@5.99.6):
    dependencies:
      '@babel/core': 7.23.0
      find-cache-dir: 3.3.2
      loader-utils: 2.0.4
      make-dir: 3.1.0
      schema-utils: 2.7.1
      webpack: 5.99.6(webpack-cli@5.1.4)

  babel-plugin-import@1.13.8:
    dependencies:
      '@babel/helper-module-imports': 7.25.9
    transitivePeerDependencies:
      - supports-color

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.23.0
      cosmiconfig: 7.1.0
      resolve: 1.22.10

  babel-plugin-polyfill-corejs2@0.4.13(@babel/core@7.23.0):
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.23.0
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.23.0)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.23.0):
    dependencies:
      '@babel/core': 7.23.0
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.23.0)
      core-js-compat: 3.41.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.4(@babel/core@7.23.0):
    dependencies:
      '@babel/core': 7.23.0
      '@babel/helper-define-polyfill-provider': 0.6.4(@babel/core@7.23.0)
    transitivePeerDependencies:
      - supports-color

  babel-runtime@6.26.0:
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  batch@0.6.1: {}

  big.js@3.2.0: {}

  big.js@5.2.2: {}

  binary-extensions@2.3.0: {}

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  blueimp-md5@2.19.0: {}

  bn.js@4.12.1: {}

  bn.js@5.2.1: {}

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bonjour-service@1.3.0:
    dependencies:
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  boolbase@1.0.0: {}

  bowser@1.9.4: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  brorand@1.1.0: {}

  browserify-aes@1.2.0:
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.6
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1

  browserify-cipher@1.0.1:
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3

  browserify-des@1.0.2:
    dependencies:
      cipher-base: 1.0.6
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1

  browserify-rsa@4.1.1:
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  browserify-sign@4.2.3:
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.6.1
      hash-base: 3.0.5
      inherits: 2.0.4
      parse-asn1: 5.1.7
      readable-stream: 2.3.8
      safe-buffer: 5.2.1

  browserify-zlib@0.2.0:
    dependencies:
      pako: 1.0.11

  browserslist@4.0.0:
    dependencies:
      caniuse-lite: 1.0.30001715
      electron-to-chromium: 1.5.139
      node-releases: 1.1.77

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001715
      electron-to-chromium: 1.5.139
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  buffer-from@0.1.2: {}

  buffer-from@1.1.2: {}

  buffer-xor@1.0.3: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  builtin-status-codes@3.0.0: {}

  bytes@3.1.2: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  caller-callsite@2.0.0:
    dependencies:
      callsites: 2.0.0

  caller-path@2.0.0:
    dependencies:
      caller-callsite: 2.0.0

  callsites@2.0.0: {}

  callsites@3.1.0: {}

  camel-case@4.1.2:
    dependencies:
      pascal-case: 3.1.2
      tslib: 2.8.1

  camelcase@6.3.0: {}

  caniuse-api@3.0.0:
    dependencies:
      browserslist: 4.0.0
      caniuse-lite: 1.0.30001715
      lodash.memoize: 4.1.2
      lodash.uniq: 4.5.0

  caniuse-lite@1.0.30001715: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chardet@0.7.0: {}

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.3
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chokidar@4.0.3:
    dependencies:
      readdirp: 4.1.2

  chrome-trace-event@1.0.4: {}

  ci-info@3.9.0: {}

  cipher-base@1.0.6:
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  classnames@2.5.1: {}

  clean-css@5.3.3:
    dependencies:
      source-map: 0.6.1

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-spinners@2.9.2: {}

  cli-width@3.0.0: {}

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  clone@1.0.4: {}

  clsx@1.2.1: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color@3.2.1:
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1

  colord@2.9.3: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@10.0.1: {}

  commander@2.20.3: {}

  commander@7.2.0: {}

  commander@8.3.0: {}

  commondir@1.0.1: {}

  component-classes@1.2.6:
    dependencies:
      component-indexof: 0.0.3

  component-indexof@0.0.3: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.54.0

  compression@1.8.0:
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  compute-scroll-into-view@3.1.1: {}

  computeds@0.0.1: {}

  concat-map@0.0.1: {}

  concurrently@8.2.2:
    dependencies:
      chalk: 4.1.2
      date-fns: 2.30.0
      lodash: 4.17.21
      rxjs: 7.8.2
      shell-quote: 1.8.2
      spawn-command: 0.0.2
      supports-color: 8.1.1
      tree-kill: 1.2.2
      yargs: 17.7.2

  connect-history-api-fallback@2.0.0: {}

  consola@2.15.3: {}

  console-browserify@1.2.0: {}

  constants-browserify@1.0.0: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  copy-to@2.0.1: {}

  copy-webpack-plugin@11.0.0(webpack@5.99.6):
    dependencies:
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      globby: 13.2.2
      normalize-path: 3.0.0
      schema-utils: 4.3.0
      serialize-javascript: 6.0.2
      webpack: 5.99.6(webpack-cli@5.1.4)

  core-js-compat@3.41.0:
    dependencies:
      browserslist: 4.24.4

  core-js-pure@3.41.0: {}

  core-js@2.6.12: {}

  core-js@3.41.0: {}

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  cos-js-sdk-v5@1.8.7:
    dependencies:
      fast-xml-parser: 4.5.0

  cosmiconfig@5.2.1:
    dependencies:
      import-fresh: 2.0.0
      is-directory: 0.3.1
      js-yaml: 3.14.1
      parse-json: 4.0.0

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cosmiconfig@8.3.6(typescript@4.9.5):
    dependencies:
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0
    optionalDependencies:
      typescript: 4.9.5

  create-ecdh@4.0.4:
    dependencies:
      bn.js: 4.12.1
      elliptic: 6.6.1

  create-hash@1.2.0:
    dependencies:
      cipher-base: 1.0.6
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11

  create-hmac@1.1.7:
    dependencies:
      cipher-base: 1.0.6
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11

  create-react-class@15.7.0:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-fetch@4.0.0:
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-browserify@3.12.1:
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.3
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      hash-base: 3.0.5
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4

  crypto-js@4.2.0: {}

  css-animation@1.6.1:
    dependencies:
      babel-runtime: 6.26.0
      component-classes: 1.2.6

  css-blank-pseudo@3.0.3(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  css-declaration-sorter@6.4.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  css-has-pseudo@3.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  css-loader@6.11.0(webpack@5.99.6):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-modules-extract-imports: 3.1.0(postcss@8.5.3)
      postcss-modules-local-by-default: 4.2.0(postcss@8.5.3)
      postcss-modules-scope: 3.2.1(postcss@8.5.3)
      postcss-modules-values: 4.0.0(postcss@8.5.3)
      postcss-value-parser: 4.2.0
      semver: 7.7.1
    optionalDependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)

  css-minimizer-webpack-plugin@4.2.2(webpack@5.99.6):
    dependencies:
      cssnano: 5.1.15(postcss@8.5.3)
      jest-worker: 29.7.0
      postcss: 8.5.3
      schema-utils: 4.3.0
      serialize-javascript: 6.0.2
      source-map: 0.6.1
      webpack: 5.99.6(webpack-cli@5.1.4)

  css-parse@2.0.0:
    dependencies:
      css: 2.2.4

  css-prefers-color-scheme@6.0.3(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  css-select@4.3.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 4.3.1
      domutils: 2.8.0
      nth-check: 2.1.1

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.2.2
      nth-check: 2.1.1

  css-selector-tokenizer@0.7.3:
    dependencies:
      cssesc: 3.0.0
      fastparse: 1.1.2

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-tree@2.2.1:
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.1

  css-tree@2.3.1:
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.1

  css-what@6.1.0: {}

  css@2.2.4:
    dependencies:
      inherits: 2.0.4
      source-map: 0.6.1
      source-map-resolve: 0.5.3
      urix: 0.1.0

  cssdb@7.11.2: {}

  cssesc@3.0.0: {}

  cssnano-preset-default@5.2.14(postcss@8.5.3):
    dependencies:
      css-declaration-sorter: 6.4.1(postcss@8.5.3)
      cssnano-utils: 3.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-calc: 8.2.4(postcss@8.5.3)
      postcss-colormin: 5.3.1(postcss@8.5.3)
      postcss-convert-values: 5.1.3(postcss@8.5.3)
      postcss-discard-comments: 5.1.2(postcss@8.5.3)
      postcss-discard-duplicates: 5.1.0(postcss@8.5.3)
      postcss-discard-empty: 5.1.1(postcss@8.5.3)
      postcss-discard-overridden: 5.1.0(postcss@8.5.3)
      postcss-merge-longhand: 5.1.7(postcss@8.5.3)
      postcss-merge-rules: 5.1.4(postcss@8.5.3)
      postcss-minify-font-values: 5.1.0(postcss@8.5.3)
      postcss-minify-gradients: 5.1.1(postcss@8.5.3)
      postcss-minify-params: 5.1.4(postcss@8.5.3)
      postcss-minify-selectors: 5.2.1(postcss@8.5.3)
      postcss-normalize-charset: 5.1.0(postcss@8.5.3)
      postcss-normalize-display-values: 5.1.0(postcss@8.5.3)
      postcss-normalize-positions: 5.1.1(postcss@8.5.3)
      postcss-normalize-repeat-style: 5.1.1(postcss@8.5.3)
      postcss-normalize-string: 5.1.0(postcss@8.5.3)
      postcss-normalize-timing-functions: 5.1.0(postcss@8.5.3)
      postcss-normalize-unicode: 5.1.1(postcss@8.5.3)
      postcss-normalize-url: 5.1.0(postcss@8.5.3)
      postcss-normalize-whitespace: 5.1.1(postcss@8.5.3)
      postcss-ordered-values: 5.1.3(postcss@8.5.3)
      postcss-reduce-initial: 5.1.2(postcss@8.5.3)
      postcss-reduce-transforms: 5.1.0(postcss@8.5.3)
      postcss-svgo: 5.1.0(postcss@8.5.3)
      postcss-unique-selectors: 5.1.1(postcss@8.5.3)

  cssnano-utils@3.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  cssnano@5.1.15(postcss@8.5.3):
    dependencies:
      cssnano-preset-default: 5.2.14(postcss@8.5.3)
      lilconfig: 2.1.0
      postcss: 8.5.3
      yaml: 1.10.2

  csso@4.2.0:
    dependencies:
      css-tree: 1.1.3

  csso@5.0.5:
    dependencies:
      css-tree: 2.2.1

  csstype@3.1.3: {}

  d3-array@3.2.4:
    dependencies:
      internmap: 2.0.3

  d3-color@3.1.0: {}

  d3-ease@3.0.1: {}

  d3-format@3.1.0: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-path@3.1.0: {}

  d3-scale@4.0.2:
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0

  d3-shape@3.2.0:
    dependencies:
      d3-path: 3.1.0

  d3-time-format@4.1.0:
    dependencies:
      d3-time: 3.1.0

  d3-time@3.1.0:
    dependencies:
      d3-array: 3.2.4

  d3-timer@3.0.1: {}

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.23.0

  dateformat@2.2.0: {}

  dayjs@1.11.13: {}

  de-indent@1.0.2: {}

  debounce@1.2.1: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.1.0:
    dependencies:
      ms: 2.0.0

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decimal.js-light@2.5.1: {}

  decimal.js@10.5.0: {}

  decode-uri-component@0.2.2: {}

  deep-is@0.1.4: {}

  deepmerge@1.5.2: {}

  deepmerge@4.3.1: {}

  default-gateway@6.0.3:
    dependencies:
      execa: 5.1.1

  default-passive-events@2.0.0: {}

  default-user-agent@1.0.0:
    dependencies:
      os-name: 1.0.3

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-lazy-prop@2.0.0: {}

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  dequal@2.0.3: {}

  des.js@1.1.0:
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  destroy@1.2.0: {}

  detect-libc@1.0.3:
    optional: true

  detect-node@2.1.0: {}

  diffie-hellman@5.0.3:
    dependencies:
      bn.js: 4.12.1
      miller-rabin: 4.0.1
      randombytes: 2.1.0

  digest-header@1.1.0: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dns-packet@5.6.1:
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.5

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  dom-align@1.12.4: {}

  dom-converter@0.2.0:
    dependencies:
      utila: 0.4.0

  dom-helpers@3.4.0:
    dependencies:
      '@babel/runtime': 7.23.0

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.23.0
      csstype: 3.1.3

  dom-serializer@1.4.1:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      entities: 2.2.0

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domain-browser@4.23.0: {}

  domelementtype@2.3.0: {}

  domhandler@4.3.1:
    dependencies:
      domelementtype: 2.3.0

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@2.8.0:
    dependencies:
      dom-serializer: 1.4.1
      domelementtype: 2.3.0
      domhandler: 4.3.1

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  dotenv-defaults@2.0.2:
    dependencies:
      dotenv: 8.6.0

  dotenv-webpack@8.1.0(webpack@5.99.6):
    dependencies:
      dotenv-defaults: 2.0.2
      webpack: 5.99.6(webpack-cli@5.1.4)

  dotenv@10.0.0: {}

  dotenv@8.6.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  duplexer2@0.1.4:
    dependencies:
      readable-stream: 2.3.8

  duplexer@0.1.2: {}

  ee-first@1.1.1: {}

  electron-to-chromium@1.5.139: {}

  elliptic@6.6.1:
    dependencies:
      bn.js: 4.12.1
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1

  emoji-regex@8.0.0: {}

  emojis-list@2.1.0: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  end-of-stream@1.4.4:
    dependencies:
      once: 1.4.0

  end-or-error@1.0.1: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  entities@2.2.0: {}

  entities@4.5.0: {}

  envinfo@7.14.0: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.23.9:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.6.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  esdk-obs-browserjs@3.24.3:
    dependencies:
      axios: 0.26.1
      blueimp-md5: 2.19.0
      crypto-js: 4.2.0
      js-base64: 3.7.7
      jssha: 3.3.1
      urijs: 1.19.11
    transitivePeerDependencies:
      - debug

  eslint-config-prettier@6.15.0(eslint@9.25.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.25.0(jiti@1.21.7)
      get-stdin: 6.0.0

  eslint-plugin-prettier@3.4.1(eslint@9.25.0(jiti@1.21.7))(prettier@2.8.8):
    dependencies:
      eslint: 9.25.0(jiti@1.21.7)
      prettier: 2.8.8
      prettier-linter-helpers: 1.0.0

  eslint-plugin-react-hooks@4.6.2(eslint@9.25.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.25.0(jiti@1.21.7)

  eslint-plugin-react@7.37.5(eslint@9.25.0(jiti@1.21.7)):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 9.25.0(jiti@1.21.7)
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@8.3.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-utils@3.0.0(eslint@9.25.0(jiti@1.21.7)):
    dependencies:
      eslint: 9.25.0(jiti@1.21.7)
      eslint-visitor-keys: 2.1.0

  eslint-visitor-keys@2.1.0: {}

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint-webpack-plugin@3.2.0(eslint@9.25.0(jiti@1.21.7))(webpack@5.99.6):
    dependencies:
      '@types/eslint': 8.56.12
      eslint: 9.25.0(jiti@1.21.7)
      jest-worker: 28.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      schema-utils: 4.3.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  eslint@9.25.0(jiti@1.21.7):
    dependencies:
      '@eslint-community/eslint-utils': 4.6.1(eslint@9.25.0(jiti@1.21.7))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.20.0
      '@eslint/config-helpers': 0.2.1
      '@eslint/core': 0.13.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.25.0
      '@eslint/plugin-kit': 0.2.8
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.2
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      escape-string-regexp: 4.0.0
      eslint-scope: 8.3.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 1.21.7
    transitivePeerDependencies:
      - supports-color

  espree@10.3.0:
    dependencies:
      acorn: 8.14.1
      acorn-jsx: 5.3.2(acorn@8.14.1)
      eslint-visitor-keys: 4.2.0

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  event-target-shim@5.0.1: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  evp_bytestokey@1.0.3:
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  express@4.21.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-equals@5.2.2: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fast-xml-parser@4.5.0:
    dependencies:
      strnum: 1.1.2

  fastest-levenshtein@1.0.16: {}

  fastparse@1.1.2: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  faye-websocket@0.11.4:
    dependencies:
      websocket-driver: 0.7.4

  fflate@0.8.2: {}

  figures@3.2.0:
    dependencies:
      escape-string-regexp: 1.0.5

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  filesize@8.0.7: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  filter-obj@2.0.2: {}

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-cache-dir@3.3.2:
    dependencies:
      commondir: 1.0.1
      make-dir: 3.1.0
      pkg-dir: 4.2.0

  find-root@1.1.0: {}

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flat@5.0.2: {}

  flatted@3.3.3: {}

  follow-redirects@1.15.9(debug@4.4.0):
    optionalDependencies:
      debug: 4.4.0

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  fork-ts-checker-webpack-plugin@7.3.0(typescript@4.9.5)(vue-template-compiler@2.7.16)(webpack@5.99.6):
    dependencies:
      '@babel/code-frame': 7.26.2
      chalk: 4.1.2
      chokidar: 3.6.0
      cosmiconfig: 7.1.0
      deepmerge: 4.3.1
      fs-extra: 10.1.0
      memfs: 3.5.3
      minimatch: 3.1.2
      node-abort-controller: 3.1.1
      schema-utils: 3.3.0
      semver: 7.7.1
      tapable: 2.2.1
      typescript: 4.9.5
      webpack: 5.99.6(webpack-cli@5.1.4)
    optionalDependencies:
      vue-template-compiler: 2.7.16

  form-data@4.0.4:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      hasown: 2.0.2
      mime-types: 2.1.35

  form-render@2.2.5(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@ant-design/icons': 4.8.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      ahooks: 3.7.5(react@18.2.0)
      antd: 5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      async-validator: 3.5.2
      classnames: 2.5.1
      color: 3.2.1
      dayjs: 1.11.13
      lodash-es: 4.17.21
      rc-color-picker: 1.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      virtualizedtableforantd4: 1.3.1(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      zustand: 4.5.6(@types/react@18.2.55)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  form-render@2.5.2(@types/react@18.2.55)(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@ant-design/icons': 4.8.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      ahooks: 3.8.4(react@18.2.0)
      antd: 5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      async-validator: 3.5.2
      classnames: 2.5.1
      color: 3.2.1
      dayjs: 1.11.13
      lodash-es: 4.17.21
      rc-color-picker: 1.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      virtualizedtableforantd4: 1.3.1(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      zustand: 4.5.6(@types/react@18.2.55)(react@18.2.0)
    transitivePeerDependencies:
      - '@types/react'
      - immer

  formstream@1.5.1:
    dependencies:
      destroy: 1.2.0
      mime: 2.6.0
      node-hex: 1.0.1
      pause-stream: 0.0.11

  forwarded@0.2.0: {}

  fraction.js@4.3.7: {}

  fresh@0.5.2: {}

  fs-extra@10.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs-monkey@1.0.6: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.2:
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functional-red-black-tree@1.0.1: {}

  functions-have-names@1.2.3: {}

  generic-names@1.0.3:
    dependencies:
      loader-utils: 0.2.17

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-ready@1.0.0: {}

  get-stdin@6.0.0: {}

  get-stream@6.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  git-promise@1.0.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@14.0.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  globby@13.2.2:
    dependencies:
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 4.0.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  gzip-size@6.0.0:
    dependencies:
      duplexer: 0.1.2

  handle-thing@2.0.1: {}

  has-bigints@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hash-base@3.0.5:
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  hash.js@1.1.7:
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  history@5.3.0:
    dependencies:
      '@babel/runtime': 7.23.0

  hmac-drbg@1.0.1:
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hpack.js@2.1.6:
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  html-entities@2.6.0: {}

  html-escaper@2.0.2: {}

  html-minifier-terser@6.1.0:
    dependencies:
      camel-case: 4.1.2
      clean-css: 5.3.3
      commander: 8.3.0
      he: 1.2.0
      param-case: 3.0.4
      relateurl: 0.2.7
      terser: 5.39.0

  html-parse-stringify@3.0.1:
    dependencies:
      void-elements: 3.1.0

  html-tokenize@2.0.1:
    dependencies:
      buffer-from: 0.1.2
      inherits: 2.0.4
      minimist: 1.2.8
      readable-stream: 1.0.34
      through2: 0.4.2

  html-webpack-plugin@5.6.3(webpack@5.99.6):
    dependencies:
      '@types/html-minifier-terser': 6.1.0
      html-minifier-terser: 6.1.0
      lodash: 4.17.21
      pretty-error: 4.0.0
      tapable: 2.2.1
    optionalDependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)

  htmlparser2@6.1.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 4.3.1
      domutils: 2.8.0
      entities: 2.2.0

  http-cookie-agent@6.0.8(tough-cookie@5.1.2):
    dependencies:
      agent-base: 7.1.3
      tough-cookie: 5.1.2

  http-deceiver@1.2.7: {}

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-parser-js@0.5.10: {}

  http-proxy-middleware@2.0.9(@types/express@4.17.21)(debug@4.4.0):
    dependencies:
      '@types/http-proxy': 1.17.16
      http-proxy: 1.18.1(debug@4.4.0)
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.8
    optionalDependencies:
      '@types/express': 4.17.21
    transitivePeerDependencies:
      - debug

  http-proxy@1.18.1(debug@4.4.0):
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.9(debug@4.4.0)
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  http@0.0.1-security: {}

  https-browserify@1.0.0: {}

  https@1.0.0: {}

  human-signals@2.1.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  i18next-browser-languagedetector@8.0.5:
    dependencies:
      '@babel/runtime': 7.27.0

  i18next-http-backend@2.7.3:
    dependencies:
      cross-fetch: 4.0.0
    transitivePeerDependencies:
      - encoding

  i18next-icu@2.3.0(intl-messageformat@10.7.16):
    dependencies:
      intl-messageformat: 10.7.16

  i18next@21.9.1:
    dependencies:
      '@babel/runtime': 7.23.0

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  icss-utils@3.0.1:
    dependencies:
      postcss: 6.0.23

  icss-utils@4.1.1:
    dependencies:
      postcss: 7.0.39

  icss-utils@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  image-size@0.5.5:
    optional: true

  image-size@1.2.1:
    dependencies:
      queue: 6.0.2

  immediate@3.0.6: {}

  immutable@5.1.1: {}

  import-cwd@2.1.0:
    dependencies:
      import-from: 2.1.0

  import-fresh@2.0.0:
    dependencies:
      caller-path: 2.0.0
      resolve-from: 3.0.0

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-from@2.1.0:
    dependencies:
      resolve-from: 3.0.0

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  inquirer@8.2.6:
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      ora: 5.4.1
      run-async: 2.4.1
      rxjs: 7.8.2
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
      wrap-ansi: 6.2.0

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  internmap@2.0.3: {}

  interpret@3.1.1: {}

  intersection-observer@0.12.2: {}

  intl-messageformat@10.7.16:
    dependencies:
      '@formatjs/ecma402-abstract': 2.3.4
      '@formatjs/fast-memoize': 2.2.7
      '@formatjs/icu-messageformat-parser': 2.11.2
      tslib: 2.8.1

  ipaddr.js@1.9.1: {}

  ipaddr.js@2.2.0: {}

  is-arguments@1.2.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-class-hotfix@0.0.6: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-directory@0.3.1: {}

  is-docker@2.2.1: {}

  is-extendable@0.1.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-interactive@1.0.0: {}

  is-map@2.0.3: {}

  is-nan@1.3.2:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-plain-obj@3.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@2.0.1: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-type-of@1.4.0:
    dependencies:
      core-util-is: 1.0.3
      is-class-hotfix: 0.0.6
      isstream: 0.1.2

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-unicode-supported@0.1.0: {}

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-what@3.14.1: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@0.0.1: {}

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  isobject@3.0.1: {}

  isstream@0.1.2: {}

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  javascript-stringify@2.1.0: {}

  jest-util@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 20.17.30
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 20.17.30
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest-worker@28.1.3:
    dependencies:
      '@types/node': 20.17.30
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest-worker@29.7.0:
    dependencies:
      '@types/node': 20.17.30
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jiti@1.21.7: {}

  js-base64@2.6.4: {}

  js-base64@3.6.0: {}

  js-base64@3.7.7: {}

  js-cookie@2.2.1: {}

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json2mq@0.2.0:
    dependencies:
      string-convert: 0.2.1

  json5@0.5.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jssha@3.3.1: {}

  jstoxml@2.2.9: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  jszip@3.10.1:
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  klona@2.0.6: {}

  ko-sleep@1.1.4:
    dependencies:
      ms: 2.1.3

  launch-editor@2.10.0:
    dependencies:
      picocolors: 1.1.1
      shell-quote: 1.8.2

  less-loader@10.2.0(less@4.3.0)(webpack@5.99.6):
    dependencies:
      klona: 2.0.6
      less: 4.3.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  less@3.13.1:
    dependencies:
      copy-anything: 2.0.6
      tslib: 1.14.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      native-request: 1.1.2
      source-map: 0.6.1

  less@4.3.0:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.8.1
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lie@3.3.0:
    dependencies:
      immediate: 3.0.6

  lil-gui@0.20.0: {}

  lilconfig@2.1.0: {}

  lines-and-columns@1.2.4: {}

  loader-runner@4.3.0: {}

  loader-utils@0.2.17:
    dependencies:
      big.js: 3.2.0
      emojis-list: 2.1.0
      json5: 0.5.1
      object-assign: 4.1.1

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-es@4.17.21: {}

  lodash.camelcase@4.3.0: {}

  lodash.debounce@4.0.8: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.uniq@4.5.0: {}

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  make-dir@3.1.0:
    dependencies:
      semver: 6.3.1

  math-intrinsics@1.1.0: {}

  md5.js@1.3.5:
    dependencies:
      hash-base: 3.0.5
      inherits: 2.0.4
      safe-buffer: 5.2.1

  mdn-data@2.0.14: {}

  mdn-data@2.0.28: {}

  mdn-data@2.0.30: {}

  media-typer@0.3.0: {}

  memfs@3.5.3:
    dependencies:
      fs-monkey: 1.0.6

  merge-descriptors@1.0.3: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  meshoptimizer@0.18.1: {}

  methods@1.1.2: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  miller-rabin@4.0.1:
    dependencies:
      bn.js: 4.12.1
      brorand: 1.1.0

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mime@2.6.0: {}

  mimic-fn@2.1.0: {}

  mini-css-extract-plugin@2.9.2(webpack@5.99.6):
    dependencies:
      schema-utils: 4.3.0
      tapable: 2.2.1
      webpack: 5.99.6(webpack-cli@5.1.4)

  minimalistic-assert@1.0.1: {}

  minimalistic-crypto-utils@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  mobx-react-lite@3.4.3(mobx@6.6.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      mobx: 6.6.1
      react: 18.2.0
    optionalDependencies:
      react-dom: 18.2.0(react@18.2.0)

  mobx-react-lite@4.1.0(mobx@6.6.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      mobx: 6.6.1
      react: 18.2.0
      use-sync-external-store: 1.5.0(react@18.2.0)
    optionalDependencies:
      react-dom: 18.2.0(react@18.2.0)

  mobx-react@7.5.2(mobx@6.6.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      mobx: 6.6.1
      mobx-react-lite: 3.4.3(mobx@6.6.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
    optionalDependencies:
      react-dom: 18.2.0(react@18.2.0)

  mobx@6.6.1: {}

  mrmime@2.0.1: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  muggle-string@0.3.1: {}

  multicast-dns@7.2.5:
    dependencies:
      dns-packet: 5.6.1
      thunky: 1.1.0

  multipipe@1.0.2:
    dependencies:
      duplexer2: 0.1.4
      object-assign: 4.1.1

  mute-stream@0.0.8: {}

  mz-modules@2.1.0:
    dependencies:
      glob: 7.2.3
      ko-sleep: 1.1.4
      mkdirp: 0.5.6
      pump: 3.0.2
      rimraf: 2.7.1

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.11: {}

  nanospinner@0.6.0:
    dependencies:
      picocolors: 1.1.1

  native-request@1.1.2:
    optional: true

  natural-compare@1.4.0: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.4.1
    optional: true

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  neo-async@2.6.2: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-abort-controller@3.1.1: {}

  node-addon-api@7.1.1:
    optional: true

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-forge@1.3.1: {}

  node-hex@1.0.1: {}

  node-polyfill-webpack-plugin@2.0.1(webpack@5.99.6):
    dependencies:
      assert: 2.1.0
      browserify-zlib: 0.2.0
      buffer: 6.0.3
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      crypto-browserify: 3.12.1
      domain-browser: 4.23.0
      events: 3.3.0
      filter-obj: 2.0.2
      https-browserify: 1.0.0
      os-browserify: 0.3.0
      path-browserify: 1.0.1
      process: 0.11.10
      punycode: 2.3.1
      querystring-es3: 0.2.1
      readable-stream: 4.7.0
      stream-browserify: 3.0.0
      stream-http: 3.2.0
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.1
      type-fest: 2.19.0
      url: 0.11.4
      util: 0.12.5
      vm-browserify: 1.1.2
      webpack: 5.99.6(webpack-cli@5.1.4)

  node-releases@1.1.77: {}

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  normalize-url@6.1.0: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1

  object-keys@0.4.0: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  obuf@1.1.2: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  opener@1.5.2: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@5.4.1:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  os-browserify@0.3.0: {}

  os-name@1.0.3:
    dependencies:
      osx-release: 1.1.0
      win-release: 1.1.1

  os-tmpdir@1.0.2: {}

  osx-release@1.1.0:
    dependencies:
      minimist: 1.2.8

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-retry@4.6.2:
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  p-try@2.2.0: {}

  pako@1.0.11: {}

  pako@2.1.0: {}

  param-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-asn1@5.1.7:
    dependencies:
      asn1.js: 4.10.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      hash-base: 3.0.5
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-node-version@1.0.1: {}

  parseurl@1.3.3: {}

  pascal-case@3.1.2:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  path-browserify@1.0.1: {}

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-to-regexp@0.1.12: {}

  path-to-regexp@8.2.0: {}

  path-type@4.0.0: {}

  pause-stream@0.0.11:
    dependencies:
      through: 2.3.8

  pbkdf2@3.1.2:
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11

  performance-now@2.1.0: {}

  picocolors@0.2.1: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  pify@4.0.1:
    optional: true

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  platform@1.3.6: {}

  playwright-core@1.52.0: {}

  playwright@1.52.0:
    dependencies:
      playwright-core: 1.52.0
    optionalDependencies:
      fsevents: 2.3.2

  possible-typed-array-names@1.1.0: {}

  postcss-attribute-case-insensitive@5.0.2(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-browser-comments@4.0.0(browserslist@4.0.0)(postcss@8.5.3):
    dependencies:
      browserslist: 4.0.0
      postcss: 8.5.3

  postcss-calc@8.2.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
      postcss-value-parser: 4.2.0

  postcss-clamp@4.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-color-functional-notation@4.2.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-color-hex-alpha@8.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-color-rebeccapurple@7.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-colormin@5.3.1(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-api: 3.0.0
      colord: 2.9.3
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-convert-values@5.1.3(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-custom-media@8.0.2(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-custom-properties@12.1.11(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-custom-selectors@6.0.3(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-dir-pseudo-class@6.0.5(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-discard-comments@5.1.2(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-discard-duplicates@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-discard-empty@5.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-discard-overridden@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-double-position-gradients@3.1.2(postcss@8.5.3):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-env-function@4.0.6(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-filter-plugins@3.0.1:
    dependencies:
      postcss: 6.0.23

  postcss-flexbugs-fixes@5.0.2(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-focus-visible@6.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-focus-within@5.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-font-variant@5.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-gap-properties@3.0.5(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-icss-keyframes@0.2.1:
    dependencies:
      icss-utils: 3.0.1
      postcss: 6.0.23
      postcss-value-parser: 3.3.1

  postcss-icss-selectors@2.0.3:
    dependencies:
      css-selector-tokenizer: 0.7.3
      generic-names: 1.0.3
      icss-utils: 3.0.1
      lodash: 4.17.21
      postcss: 6.0.23

  postcss-image-set-function@4.0.7(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-initial@4.0.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-lab-function@4.2.1(postcss@8.5.3):
    dependencies:
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-load-config@2.1.2:
    dependencies:
      cosmiconfig: 5.2.1
      import-cwd: 2.1.0

  postcss-load-config@3.1.4(postcss@8.5.3):
    dependencies:
      lilconfig: 2.1.0
      yaml: 1.10.2
    optionalDependencies:
      postcss: 8.5.3

  postcss-loader@7.3.4(postcss@8.5.3)(typescript@4.9.5)(webpack@5.99.6):
    dependencies:
      cosmiconfig: 8.3.6(typescript@4.9.5)
      jiti: 1.21.7
      postcss: 8.5.3
      semver: 7.7.1
      webpack: 5.99.6(webpack-cli@5.1.4)
    transitivePeerDependencies:
      - typescript

  postcss-logical@5.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-media-minmax@5.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-merge-longhand@5.1.7(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      stylehacks: 5.1.1(postcss@8.5.3)

  postcss-merge-rules@5.1.4(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-api: 3.0.0
      cssnano-utils: 3.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-minify-font-values@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-minify-gradients@5.1.1(postcss@8.5.3):
    dependencies:
      colord: 2.9.3
      cssnano-utils: 3.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-minify-params@5.1.4(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      cssnano-utils: 3.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-minify-selectors@5.2.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-modules-extract-imports@3.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-modules-local-by-default@4.2.0(postcss@8.5.3):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.2.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 7.1.0

  postcss-modules-values@4.0.0(postcss@8.5.3):
    dependencies:
      icss-utils: 5.1.0(postcss@8.5.3)
      postcss: 8.5.3

  postcss-nesting@10.2.0(postcss@8.5.3):
    dependencies:
      '@csstools/selector-specificity': 2.2.0(postcss-selector-parser@6.1.2)
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-normalize-charset@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-normalize-display-values@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-positions@5.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-repeat-style@5.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-string@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-timing-functions@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-unicode@5.1.1(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-url@5.1.0(postcss@8.5.3):
    dependencies:
      normalize-url: 6.1.0
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize-whitespace@5.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-normalize@10.0.1(browserslist@4.0.0)(postcss@8.5.3):
    dependencies:
      '@csstools/normalize.css': 12.1.1
      browserslist: 4.0.0
      postcss: 8.5.3
      postcss-browser-comments: 4.0.0(browserslist@4.0.0)(postcss@8.5.3)
      sanitize.css: 13.0.0

  postcss-opacity-percentage@1.1.3(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-ordered-values@5.1.3(postcss@8.5.3):
    dependencies:
      cssnano-utils: 3.1.0(postcss@8.5.3)
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-overflow-shorthand@3.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-page-break@3.0.4(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-place@7.0.5(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-preset-env@7.8.3(postcss@8.5.3):
    dependencies:
      '@csstools/postcss-cascade-layers': 1.1.1(postcss@8.5.3)
      '@csstools/postcss-color-function': 1.1.1(postcss@8.5.3)
      '@csstools/postcss-font-format-keywords': 1.0.1(postcss@8.5.3)
      '@csstools/postcss-hwb-function': 1.0.2(postcss@8.5.3)
      '@csstools/postcss-ic-unit': 1.0.1(postcss@8.5.3)
      '@csstools/postcss-is-pseudo-class': 2.0.7(postcss@8.5.3)
      '@csstools/postcss-nested-calc': 1.0.0(postcss@8.5.3)
      '@csstools/postcss-normalize-display-values': 1.0.1(postcss@8.5.3)
      '@csstools/postcss-oklab-function': 1.1.1(postcss@8.5.3)
      '@csstools/postcss-progressive-custom-properties': 1.3.0(postcss@8.5.3)
      '@csstools/postcss-stepped-value-functions': 1.0.1(postcss@8.5.3)
      '@csstools/postcss-text-decoration-shorthand': 1.0.0(postcss@8.5.3)
      '@csstools/postcss-trigonometric-functions': 1.0.2(postcss@8.5.3)
      '@csstools/postcss-unset-value': 1.0.2(postcss@8.5.3)
      autoprefixer: 10.4.21(postcss@8.5.3)
      browserslist: 4.24.4
      css-blank-pseudo: 3.0.3(postcss@8.5.3)
      css-has-pseudo: 3.0.4(postcss@8.5.3)
      css-prefers-color-scheme: 6.0.3(postcss@8.5.3)
      cssdb: 7.11.2
      postcss: 8.5.3
      postcss-attribute-case-insensitive: 5.0.2(postcss@8.5.3)
      postcss-clamp: 4.1.0(postcss@8.5.3)
      postcss-color-functional-notation: 4.2.4(postcss@8.5.3)
      postcss-color-hex-alpha: 8.0.4(postcss@8.5.3)
      postcss-color-rebeccapurple: 7.1.1(postcss@8.5.3)
      postcss-custom-media: 8.0.2(postcss@8.5.3)
      postcss-custom-properties: 12.1.11(postcss@8.5.3)
      postcss-custom-selectors: 6.0.3(postcss@8.5.3)
      postcss-dir-pseudo-class: 6.0.5(postcss@8.5.3)
      postcss-double-position-gradients: 3.1.2(postcss@8.5.3)
      postcss-env-function: 4.0.6(postcss@8.5.3)
      postcss-focus-visible: 6.0.4(postcss@8.5.3)
      postcss-focus-within: 5.0.4(postcss@8.5.3)
      postcss-font-variant: 5.0.0(postcss@8.5.3)
      postcss-gap-properties: 3.0.5(postcss@8.5.3)
      postcss-image-set-function: 4.0.7(postcss@8.5.3)
      postcss-initial: 4.0.1(postcss@8.5.3)
      postcss-lab-function: 4.2.1(postcss@8.5.3)
      postcss-logical: 5.0.4(postcss@8.5.3)
      postcss-media-minmax: 5.0.0(postcss@8.5.3)
      postcss-nesting: 10.2.0(postcss@8.5.3)
      postcss-opacity-percentage: 1.1.3(postcss@8.5.3)
      postcss-overflow-shorthand: 3.0.4(postcss@8.5.3)
      postcss-page-break: 3.0.4(postcss@8.5.3)
      postcss-place: 7.0.5(postcss@8.5.3)
      postcss-pseudo-class-any-link: 7.1.6(postcss@8.5.3)
      postcss-replace-overflow-wrap: 4.0.0(postcss@8.5.3)
      postcss-selector-not: 6.0.1(postcss@8.5.3)
      postcss-value-parser: 4.2.0

  postcss-pseudo-class-any-link@7.1.6(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-pxtorem@6.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-reduce-initial@5.1.2(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      caniuse-api: 3.0.0
      postcss: 8.5.3

  postcss-reduce-transforms@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  postcss-rem@2.0.4:
    dependencies:
      postcss: 8.5.3
      startijenn-rem: 1.1.1

  postcss-replace-overflow-wrap@4.0.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3

  postcss-selector-not@6.0.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-selector-parser@6.1.2:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-selector-parser@7.1.0:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-svgo@5.1.0(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      svgo: 2.8.0

  postcss-unique-selectors@5.1.1(postcss@8.5.3):
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  postcss-value-parser@3.3.1: {}

  postcss-value-parser@4.2.0: {}

  postcss@6.0.23:
    dependencies:
      chalk: 2.4.2
      source-map: 0.6.1
      supports-color: 5.5.0

  postcss@7.0.39:
    dependencies:
      picocolors: 0.2.1
      source-map: 0.6.1

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  pptxgenjs@3.12.0:
    dependencies:
      '@types/node': 18.19.86
      https: 1.0.0
      image-size: 1.2.1
      jszip: 3.10.1

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@2.8.8: {}

  pretty-error@4.0.0:
    dependencies:
      lodash: 4.17.21
      renderkid: 3.0.0

  pretty-time@1.1.0: {}

  process-nextick-args@2.0.1: {}

  process@0.11.10: {}

  promise@8.3.0:
    dependencies:
      asap: 2.0.6

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  proxy-from-env@1.1.0: {}

  prr@1.0.1:
    optional: true

  public-encrypt@4.0.3:
    dependencies:
      bn.js: 4.12.1
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      parse-asn1: 5.1.7
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  pump@3.0.2:
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0

  punycode@1.4.1: {}

  punycode@2.3.1: {}

  qs@6.13.0:
    dependencies:
      side-channel: 1.1.0

  qs@6.14.0:
    dependencies:
      side-channel: 1.1.0

  querystring-es3@0.2.1: {}

  queue-microtask@1.2.3: {}

  queue@6.0.2:
    dependencies:
      inherits: 2.0.4

  raf@3.4.1:
    dependencies:
      performance-now: 2.1.0

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  randomfill@1.0.4:
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  rc-align@2.4.5:
    dependencies:
      babel-runtime: 6.26.0
      dom-align: 1.12.4
      prop-types: 15.8.1
      rc-util: 4.21.1

  rc-animate@2.11.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
      css-animation: 1.6.1
      prop-types: 15.8.1
      raf: 3.4.1
      rc-util: 4.21.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-lifecycles-compat: 3.0.4

  rc-cascader@3.33.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.27.0
      classnames: 2.5.1
      rc-select: 14.16.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-tree: 5.13.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-checkbox@3.5.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-collapse@3.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-color-picker@1.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      classnames: 2.5.1
      prop-types: 15.8.1
      rc-trigger: 1.11.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 4.21.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      tinycolor2: 1.6.0

  rc-dialog@9.6.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-drawer@7.2.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.27.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-dropdown@4.2.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-field-form@2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/async-validator': 5.0.4
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-image@7.11.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/portal': 1.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-dialog: 9.6.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-input-number@9.4.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/mini-decimal': 1.1.0
      classnames: 2.5.1
      rc-input: 1.7.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-input@1.7.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-mentions@2.19.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-input: 1.7.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-menu: 9.16.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-textarea: 1.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-menu@9.16.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-overflow: 1.4.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-motion@2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-notification@5.6.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-overflow@1.4.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-pagination@5.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-picker@4.11.3(date-fns@2.30.0)(dayjs@1.11.13)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.27.0
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-overflow: 1.4.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
    optionalDependencies:
      date-fns: 2.30.0
      dayjs: 1.11.13

  rc-progress@4.0.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-rate@2.13.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-resize-observer@0.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      resize-observer-polyfill: 1.5.1

  rc-resize-observer@1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      resize-observer-polyfill: 1.5.1

  rc-segmented@2.7.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-select@14.16.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-overflow: 1.4.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-virtual-list: 3.18.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-slider@11.1.8(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-steps@6.0.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-switch@4.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-table@7.50.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/context': 1.4.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-virtual-list: 3.18.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-tabs@15.5.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-dropdown: 4.2.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-menu: 9.16.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-textarea@1.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-input: 1.7.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-tooltip@6.4.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      '@rc-component/trigger': 2.2.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-tree-select@5.27.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.27.0
      classnames: 2.5.1
      rc-select: 14.16.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-tree: 5.13.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-tree@5.13.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-motion: 2.9.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-virtual-list: 3.18.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-trigger@1.11.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      babel-runtime: 6.26.0
      create-react-class: 15.7.0
      prop-types: 15.8.1
      rc-align: 2.4.5
      rc-animate: 2.11.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 4.21.1
    transitivePeerDependencies:
      - react
      - react-dom

  rc-upload@4.8.1(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  rc-util@4.21.1:
    dependencies:
      add-dom-event-listener: 1.1.0
      prop-types: 15.8.1
      react-is: 16.13.1
      react-lifecycles-compat: 3.0.4
      shallowequal: 1.1.0

  rc-util@5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 18.3.1

  rc-virtual-list@3.18.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      classnames: 2.5.1
      rc-resize-observer: 1.4.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      rc-util: 5.44.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  react-app-polyfill@2.0.0:
    dependencies:
      core-js: 3.41.0
      object-assign: 4.1.1
      promise: 8.3.0
      raf: 3.4.1
      regenerator-runtime: 0.13.11
      whatwg-fetch: 3.6.20

  react-device-detect@2.2.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      ua-parser-js: 1.0.40

  react-dom@18.2.0(react@18.2.0):
    dependencies:
      loose-envify: 1.4.0
      react: 18.2.0
      scheduler: 0.23.2

  react-draggable@4.4.6(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      clsx: 1.2.1
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  react-fast-compare@3.2.2: {}

  react-i18next@11.18.4(i18next@21.9.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      html-parse-stringify: 3.0.1
      i18next: 21.9.1
      react: 18.2.0
    optionalDependencies:
      react-dom: 18.2.0(react@18.2.0)

  react-if@4.1.6(react@18.2.0):
    dependencies:
      react: 18.2.0

  react-is@16.13.1: {}

  react-is@18.3.1: {}

  react-lifecycles-compat@3.0.4: {}

  react-refresh@0.14.2: {}

  react-resize-detector@8.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      lodash: 4.17.21
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  react-router-dom@6.3.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      history: 5.3.0
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-router: 6.3.0(react@18.2.0)

  react-router@6.3.0(react@18.2.0):
    dependencies:
      history: 5.3.0
      react: 18.2.0

  react-smooth@2.0.5(prop-types@15.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      fast-equals: 5.2.2
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-transition-group: 2.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)

  react-transition-group@2.9.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      dom-helpers: 3.4.0
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-lifecycles-compat: 3.0.4

  react-transition-group@4.4.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      '@babel/runtime': 7.23.0
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  react@18.2.0:
    dependencies:
      loose-envify: 1.4.0

  reactcss@1.2.3(react@18.2.0):
    dependencies:
      lodash: 4.17.21
      react: 18.2.0

  readable-stream@1.0.34:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readable-stream@4.7.0:
    dependencies:
      abort-controller: 3.0.0
      buffer: 6.0.3
      events: 3.3.0
      process: 0.11.10
      string_decoder: 1.3.0

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  readdirp@4.1.2: {}

  recharts-scale@0.4.5:
    dependencies:
      decimal.js-light: 2.5.1

  recharts@2.9.2(prop-types@15.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      classnames: 2.5.1
      eventemitter3: 4.0.7
      lodash: 4.17.21
      prop-types: 15.8.1
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)
      react-is: 16.13.1
      react-resize-detector: 8.1.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react-smooth: 2.0.5(prop-types@15.8.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      recharts-scale: 0.4.5
      tiny-invariant: 1.3.3
      victory-vendor: 36.9.2

  rechoir@0.8.0:
    dependencies:
      resolve: 1.22.10

  recursive-readdir@2.2.3:
    dependencies:
      minimatch: 3.1.2

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.11.1: {}

  regenerator-runtime@0.13.11: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.23.0

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpp@3.2.0: {}

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  relateurl@0.2.7: {}

  renderkid@3.0.0:
    dependencies:
      css-select: 4.3.0
      dom-converter: 0.2.0
      htmlparser2: 6.1.0
      lodash: 4.17.21
      strip-ansi: 6.0.1

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requires-port@1.0.0: {}

  reserved-words@0.1.2: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@3.0.0: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-url@0.2.1: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  retry@0.13.1: {}

  reusify@1.1.0: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  ripemd160@2.0.2:
    dependencies:
      hash-base: 3.0.5
      inherits: 2.0.4

  run-async@2.4.1: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  sa-sdk-javascript@1.16.3: {}

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safe-stable-stringify@2.5.0: {}

  safer-buffer@2.1.2: {}

  sanitize.css@13.0.0: {}

  sass-loader@12.6.0(sass@1.86.3)(webpack@5.99.6):
    dependencies:
      klona: 2.0.6
      neo-async: 2.6.2
      webpack: 5.99.6(webpack-cli@5.1.4)
    optionalDependencies:
      sass: 1.86.3

  sass@1.86.3:
    dependencies:
      chokidar: 4.0.3
      immutable: 5.1.1
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.1

  sax@1.2.4: {}

  sax@1.4.1: {}

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  schema-utils@2.7.1:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@3.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@4.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  screenfull@5.2.0: {}

  scroll-into-view-if-needed@3.1.0:
    dependencies:
      compute-scroll-into-view: 3.1.1

  sdk-base@2.0.1:
    dependencies:
      get-ready: 1.0.0

  select-hose@2.0.0: {}

  selfsigned@2.4.1:
    dependencies:
      '@types/node-forge': 1.3.11
      node-forge: 1.3.1

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.7.1: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1:
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setimmediate@1.0.5: {}

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  sha.js@2.4.11:
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shallowequal@1.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.2: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  sirv@2.0.4:
    dependencies:
      '@polka/url': 1.0.0-next.29
      mrmime: 2.0.1
      totalist: 3.0.1

  slash@3.0.0: {}

  slash@4.0.0: {}

  snake-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  sockjs@0.3.24:
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  source-list-map@2.0.1: {}

  source-map-js@1.2.1: {}

  source-map-resolve@0.5.3:
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map-url@0.4.1: {}

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  spark-md5@3.0.2: {}

  spawn-command@0.0.2: {}

  spdy-transport@3.0.0:
    dependencies:
      debug: 4.4.0
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  spdy@4.0.2:
    dependencies:
      debug: 4.4.0
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  sprintf-js@1.0.3: {}

  stable@0.1.8: {}

  stackframe@1.3.4: {}

  startijenn-rem@1.1.1: {}

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  std-env@3.9.0: {}

  stream-browserify@3.0.0:
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2

  stream-http@2.8.2:
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 2.3.8
      to-arraybuffer: 1.0.1
      xtend: 4.0.2

  stream-http@3.2.0:
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2
      xtend: 4.0.2

  stream-wormhole@1.1.0: {}

  string-convert@0.2.1: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.9

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string_decoder@0.10.31: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.0:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-bom@3.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-json-comments@3.1.1: {}

  strnum@1.1.2: {}

  style-loader@3.3.4(webpack@5.99.6):
    dependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)

  stylehacks@5.1.1(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.4
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2

  stylis@4.2.0: {}

  stylis@4.3.6: {}

  stylus@0.54.8:
    dependencies:
      css-parse: 2.0.0
      debug: 3.1.0
      glob: 7.2.3
      mkdirp: 1.0.4
      safer-buffer: 2.1.2
      sax: 1.2.4
      semver: 6.3.1
      source-map: 0.7.4
    transitivePeerDependencies:
      - supports-color

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-parser@2.0.4: {}

  svgo@2.8.0:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 4.3.0
      css-tree: 1.1.3
      csso: 4.2.0
      picocolors: 1.1.1
      stable: 0.1.8

  svgo@3.3.2:
    dependencies:
      '@trysound/sax': 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.1.1

  swr@2.3.3(react@18.2.0):
    dependencies:
      dequal: 2.0.3
      react: 18.2.0
      use-sync-external-store: 1.5.0(react@18.2.0)

  tapable@2.2.1: {}

  terser-webpack-plugin@5.3.14(webpack@5.99.6):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 4.3.0
      serialize-javascript: 6.0.2
      terser: 5.39.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  terser@5.39.0:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.14.1
      commander: 2.20.3
      source-map-support: 0.5.21

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  three@0.171.0: {}

  throttle-debounce@5.0.2: {}

  through2@0.4.2:
    dependencies:
      readable-stream: 1.0.34
      xtend: 2.1.2

  through@2.3.8: {}

  thunky@1.1.0: {}

  timers-browserify@2.0.12:
    dependencies:
      setimmediate: 1.0.5

  tiny-invariant@1.3.3: {}

  tinycolor2@1.6.0: {}

  tldts-core@6.1.86: {}

  tldts@6.1.86:
    dependencies:
      tldts-core: 6.1.86

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  to-arraybuffer@1.0.1: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  toidentifier@1.0.1: {}

  tos-crc64-js@0.0.1: {}

  totalist@3.0.1: {}

  tough-cookie@5.1.2:
    dependencies:
      tldts: 6.1.86

  tr46@0.0.3: {}

  tree-kill@1.2.2: {}

  ts-api-utils@2.1.0(typescript@5.3.3):
    dependencies:
      typescript: 5.3.3

  ts-loader@9.5.2(typescript@5.3.3)(webpack@5.99.6):
    dependencies:
      chalk: 4.1.2
      enhanced-resolve: 5.18.1
      micromatch: 4.0.8
      semver: 7.7.1
      source-map: 0.7.4
      typescript: 5.3.3
      webpack: 5.99.6(webpack-cli@5.1.4)

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@5.3.3):
    dependencies:
      tslib: 1.14.1
      typescript: 5.3.3

  tty-browserify@0.0.1: {}

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-fest@0.21.3: {}

  type-fest@1.4.0: {}

  type-fest@2.19.0: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript-eslint@8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.30.1(@typescript-eslint/parser@8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3))(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/parser': 8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      '@typescript-eslint/utils': 8.30.1(eslint@9.25.0(jiti@1.21.7))(typescript@5.3.3)
      eslint: 9.25.0(jiti@1.21.7)
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  typescript-plugin-css-modules@2.8.0(typescript@5.3.3):
    dependencies:
      dotenv: 8.6.0
      icss-utils: 4.1.1
      less: 3.13.1
      lodash.camelcase: 4.3.0
      postcss: 7.0.39
      postcss-filter-plugins: 3.0.1
      postcss-icss-selectors: 2.0.3
      postcss-load-config: 2.1.2
      reserved-words: 0.1.2
      sass: 1.86.3
      stylus: 0.54.8
      tsconfig-paths: 3.15.0
      typescript: 5.3.3
    transitivePeerDependencies:
      - supports-color

  typescript-plugin-css-modules@3.4.0(typescript@4.9.5):
    dependencies:
      dotenv: 10.0.0
      icss-utils: 5.1.0(postcss@8.5.3)
      less: 4.3.0
      lodash.camelcase: 4.3.0
      postcss: 8.5.3
      postcss-filter-plugins: 3.0.1
      postcss-icss-keyframes: 0.2.1
      postcss-icss-selectors: 2.0.3
      postcss-load-config: 3.1.4(postcss@8.5.3)
      reserved-words: 0.1.2
      sass: 1.86.3
      stylus: 0.54.8
      tsconfig-paths: 3.15.0
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color
      - ts-node

  typescript@4.9.5: {}

  typescript@5.3.3: {}

  ua-parser-js@1.0.40: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@5.26.5: {}

  undici-types@6.19.8: {}

  unescape@1.0.1:
    dependencies:
      extend-shallow: 2.0.1

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  urijs@1.19.11: {}

  urix@0.1.0: {}

  url-loader@4.1.1(webpack@5.99.6):
    dependencies:
      loader-utils: 2.0.4
      mime-types: 2.1.35
      schema-utils: 3.3.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  url@0.11.4:
    dependencies:
      punycode: 1.4.1
      qs: 6.14.0

  urllib@2.44.0:
    dependencies:
      any-promise: 1.3.0
      content-type: 1.0.5
      default-user-agent: 1.0.0
      digest-header: 1.1.0
      ee-first: 1.1.1
      formstream: 1.5.1
      humanize-ms: 1.2.1
      iconv-lite: 0.6.3
      pump: 3.0.2
      qs: 6.14.0
      statuses: 1.5.0
      utility: 1.18.0

  use-merge-value@1.2.0(react@18.2.0):
    dependencies:
      react: 18.2.0

  use-sync-external-store@1.5.0(react@18.2.0):
    dependencies:
      react: 18.2.0

  util-deprecate@1.0.2: {}

  util@0.12.5:
    dependencies:
      inherits: 2.0.4
      is-arguments: 1.2.0
      is-generator-function: 1.1.0
      is-typed-array: 1.1.15
      which-typed-array: 1.1.19

  utila@0.4.0: {}

  utility@1.18.0:
    dependencies:
      copy-to: 2.0.1
      escape-html: 1.0.3
      mkdirp: 0.5.6
      mz: 2.7.0
      unescape: 1.0.1

  utils-merge@1.0.1: {}

  uuid@8.3.2: {}

  uuid@9.0.1: {}

  vary@1.1.2: {}

  victory-vendor@36.9.2:
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/d3-ease': 3.0.2
      '@types/d3-interpolate': 3.0.4
      '@types/d3-scale': 4.0.9
      '@types/d3-shape': 3.1.7
      '@types/d3-time': 3.0.4
      '@types/d3-timer': 3.0.2
      d3-array: 3.2.4
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-scale: 4.0.2
      d3-shape: 3.2.0
      d3-time: 3.1.0
      d3-timer: 3.0.1

  virtualizedtableforantd4@1.3.1(antd@5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    dependencies:
      antd: 5.24.3(date-fns@2.30.0)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)
      react: 18.2.0
      react-dom: 18.2.0(react@18.2.0)

  vm-browserify@1.1.2: {}

  void-elements@3.1.0: {}

  vue-template-compiler@2.7.16:
    dependencies:
      de-indent: 1.0.2
      he: 1.2.0

  vue-tsc@1.8.27(typescript@4.9.5):
    dependencies:
      '@volar/typescript': 1.11.1
      '@vue/language-core': 1.8.27(typescript@4.9.5)
      semver: 7.7.1
      typescript: 4.9.5

  warning@4.0.3:
    dependencies:
      loose-envify: 1.4.0

  watchpack@2.4.2:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  wbuf@1.7.3:
    dependencies:
      minimalistic-assert: 1.0.1

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  webidl-conversions@3.0.1: {}

  webpack-bundle-analyzer@4.10.2:
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      acorn: 8.14.1
      acorn-walk: 8.3.4
      commander: 7.2.0
      debounce: 1.2.1
      escape-string-regexp: 4.0.0
      gzip-size: 6.0.0
      html-escaper: 2.0.2
      opener: 1.5.2
      picocolors: 1.1.1
      sirv: 2.0.4
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  webpack-chain@6.5.1:
    dependencies:
      deepmerge: 1.5.2
      javascript-stringify: 2.1.0

  webpack-cli@5.1.4(webpack-dev-server@4.15.2)(webpack@5.99.6):
    dependencies:
      '@discoveryjs/json-ext': 0.5.7
      '@webpack-cli/configtest': 2.1.1(webpack-cli@5.1.4)(webpack@5.99.6)
      '@webpack-cli/info': 2.0.2(webpack-cli@5.1.4)(webpack@5.99.6)
      '@webpack-cli/serve': 2.0.5(webpack-cli@5.1.4)(webpack-dev-server@4.15.2)(webpack@5.99.6)
      colorette: 2.0.20
      commander: 10.0.1
      cross-spawn: 7.0.6
      envinfo: 7.14.0
      fastest-levenshtein: 1.0.16
      import-local: 3.2.0
      interpret: 3.1.1
      rechoir: 0.8.0
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-merge: 5.10.0
    optionalDependencies:
      webpack-dev-server: 4.15.2(webpack-cli@5.1.4)(webpack@5.99.6)

  webpack-dev-middleware@5.3.4(webpack@5.99.6):
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.3.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  webpack-dev-server@4.15.2(webpack-cli@5.1.4)(webpack@5.99.6):
    dependencies:
      '@types/bonjour': 3.5.13
      '@types/connect-history-api-fallback': 1.5.4
      '@types/express': 4.17.21
      '@types/serve-index': 1.9.4
      '@types/serve-static': 1.15.7
      '@types/sockjs': 0.3.36
      '@types/ws': 8.18.1
      ansi-html-community: 0.0.8
      bonjour-service: 1.3.0
      chokidar: 3.6.0
      colorette: 2.0.20
      compression: 1.8.0
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.21.2
      graceful-fs: 4.2.11
      html-entities: 2.6.0
      http-proxy-middleware: 2.0.9(@types/express@4.17.21)(debug@4.4.0)
      ipaddr.js: 2.2.0
      launch-editor: 2.10.0
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.3.0
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack-dev-middleware: 5.3.4(webpack@5.99.6)
      ws: 8.18.1
    optionalDependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-cli: 5.1.4(webpack-dev-server@4.15.2)(webpack@5.99.6)
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  webpack-federated-stats-plugin@2.0.9(webpack@5.99.6):
    dependencies:
      webpack: 5.99.6(webpack-cli@5.1.4)

  webpack-manifest-plugin@5.0.1(webpack@5.99.6):
    dependencies:
      tapable: 2.2.1
      webpack: 5.99.6(webpack-cli@5.1.4)
      webpack-sources: 2.3.1

  webpack-merge@5.10.0:
    dependencies:
      clone-deep: 4.0.1
      flat: 5.0.2
      wildcard: 2.0.1

  webpack-sources@2.3.1:
    dependencies:
      source-list-map: 2.0.1
      source-map: 0.6.1

  webpack-sources@3.2.3: {}

  webpack@5.99.6(webpack-cli@5.1.4):
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.7
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.14.1
      browserslist: 4.24.4
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.1
      es-module-lexer: 1.6.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.0
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.14(webpack@5.99.6)
      watchpack: 2.4.2
      webpack-sources: 3.2.3
    optionalDependencies:
      webpack-cli: 5.1.4(webpack-dev-server@4.15.2)(webpack@5.99.6)
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  webpackbar@5.0.2(webpack@5.99.6):
    dependencies:
      chalk: 4.1.2
      consola: 2.15.3
      pretty-time: 1.1.0
      std-env: 3.9.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  websocket-driver@0.7.4:
    dependencies:
      http-parser-js: 0.5.10
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  websocket-extensions@0.1.4: {}

  webuploader@0.1.8: {}

  whatwg-fetch@3.6.20: {}

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wildcard@2.0.1: {}

  win-release@1.1.1:
    dependencies:
      semver: 5.7.2

  word-wrap@1.2.5: {}

  worker-loader@3.0.8(webpack@5.99.6):
    dependencies:
      loader-utils: 2.0.4
      schema-utils: 3.3.0
      webpack: 5.99.6(webpack-cli@5.1.4)

  wrap-ansi@6.2.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrappy@1.0.2: {}

  ws@7.5.10: {}

  ws@8.18.1: {}

  xml2js@0.4.23:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xtend@2.1.2:
    dependencies:
      object-keys: 0.4.0

  xtend@4.0.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yaml@1.10.2: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  zustand@4.5.6(@types/react@18.2.55)(react@18.2.0):
    dependencies:
      use-sync-external-store: 1.5.0(react@18.2.0)
    optionalDependencies:
      '@types/react': 18.2.55
      react: 18.2.0
