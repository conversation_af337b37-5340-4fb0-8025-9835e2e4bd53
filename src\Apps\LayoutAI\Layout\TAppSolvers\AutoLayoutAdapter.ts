import { AutoLayoutService } from "../../Services/Layout/AutoLayoutService";
import { ILayoutStyle } from "../../Services/Layout/ILayoutStyle";
import { LayoutRoom } from "../../Services/Layout/LayoutRoom";
import { TFigureElement } from "../TFigureElements/TFigureElement";
import { TFigureList } from "../TFigureElements/TFigureList";
import { TRoomLayoutScheme } from "../TLayoutScheme/TRoomLayoutScheme";
import { TRoom } from "../TRoom";


/**
* @description 自动布局适配器,桥接新旧布局规则
* <AUTHOR>
* @date 2025-08-04
* @lastEditTime 2025-08-04 15:53:02
* @lastEditors xuld
*/
export class AutoLayoutAdapter {
    // 是否使用新布局规则
    public static is_new_bed_room_layout_rule = false;

    public static hasNewLayout(roomName: string) {
        if (!this.is_new_bed_room_layout_rule) {
            return false;
        }
        if (roomName == "卧室") {
            return true;
        }
        return false;
    }

    public static applyAutoLayout(room: TRoom) {
        let result_scheme_list: TRoomLayoutScheme[] = [];
        let layoutRoom = new LayoutRoom(room._room_entity);
        let layout_styles = AutoLayoutService.instance.getLayoutStyles(layoutRoom) || [];
        for (let i = 0; i < layout_styles.length; i++) {
            const layoutStyle: ILayoutStyle = layout_styles[i];
            let layout_scheme = this._createLayoutScheme(i, layoutStyle, room);
            result_scheme_list.push(layout_scheme);
        }
        return result_scheme_list;
    }

    private static _createLayoutScheme(i: number, layout_style: ILayoutStyle, room: TRoom): TRoomLayoutScheme {
        const figure_elements: TFigureElement[] = [];
        for (let figure of layout_style.figures) {
            const figure_element = new TFigureElement({
                category: figure.category,
                rect: figure.rect,
            });
            figure_elements.push(figure_element);
        }
        figure_elements.sort((a, b) => b.default_drawing_order - a.default_drawing_order);

        let layout_scheme = new TRoomLayoutScheme();
        layout_scheme.figure_list = new TFigureList({
            target_room_names: room.roomname,
            figure_elements: figure_elements as any
        });
        layout_scheme._scheme_name = `卧室布局-${i + 1}`;
        layout_scheme.room = room;
        layout_scheme.computeScores();
        return layout_scheme;
    }
}
(globalThis as any).AutoLayoutAdapter = AutoLayoutAdapter;